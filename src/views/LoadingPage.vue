<template>
  <div class="loading-page">
    <div class="loading-container">
      <div class="loading-content">
        <!-- 应用图标/Logo -->
        <div class="app-logo">
          <div class="logo-icon">🐠</div>
          <h1>Goldfish Monitor</h1>
          <p class="subtitle">闲鱼监控助手</p>
        </div>

        <!-- 加载动画 -->
        <div class="loading-spinner"></div>

        <!-- 状态信息 -->
        <div class="status-text">{{ loadingMessage }}</div>

        <!-- 版本信息 -->
        <div class="version-info">
          <p>版本 1.0.0 | 安全加密保护</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { useAppStore } from "../stores/app";

// 使用状态管理和路由
const router = useRouter();
const appStore = useAppStore();

// 计算属性
const loadingMessage = computed(() => appStore.loadingMessage);
const appState = computed(() => appStore.appState);
const isActivated = computed(() => appStore.isActivated);

// 监听状态变化并处理路由跳转
watch([appState, isActivated], ([newState, newActivated]) => {
  console.log("🔄 Loading页面检测到状态变化:", { newState, newActivated });

  // 根据状态跳转到对应页面
  if (
    newState === "activation_required" ||
    (newState === "not_activated" && !newActivated)
  ) {
    console.log("🔐 跳转到激活页面");
    router.push("/activation");
  } else if (newState === "activated" || newState === "running") {
    console.log("✅ 跳转到主页面");
    router.push("/");
  }
});

// 页面加载
onMounted(async () => {
  console.log("🔄 Loading页面加载，初始化应用状态管理");

  // 初始化事件监听器
  await appStore.initializeEventListeners();

  console.log("✅ Loading页面初始化完成");
});

// 组件卸载时清理
onUnmounted(() => {
  console.log("🧹 Loading页面卸载");
});
</script>

<style scoped>
.loading-page {
  height: 100vh;
  width: 100vw;
  /* 暗主题渐变背景 - 写死暗色主题 */
  background: linear-gradient(
    135deg,
    #1a1a2e 0%,
    #16213e 25%,
    #0f3460 50%,
    #533483 75%,
    #1a1a2e 100%
  );
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  /* 添加微妙的纹理效果 */
  position: relative;
}

.loading-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(120, 119, 198, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 119, 198, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(120, 219, 255, 0.1) 0%,
      transparent 50%
    );
  animation: floatingBubbles 12s ease-in-out infinite;
}

.loading-container {
  /* 暗主题容器样式 - 写死暗色主题 */
  background: rgba(24, 24, 28, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 8px 16px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 60px 40px;
  text-align: center;
  max-width: 500px;
  width: 90%;
  animation: fadeInUp 0.8s ease-out, floatContainer 6s ease-in-out infinite;
  position: relative;
  z-index: 1;
}

.app-logo {
  margin-bottom: 40px;
}

.logo-icon {
  font-size: 64px;
  margin-bottom: 16px;
  animation: pulse 2s infinite;
}

.app-logo h1 {
  /* 暗主题文字颜色 - 写死暗色主题 */
  color: #ffffff;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.subtitle {
  color: #a0a0a0;
  font-size: 16px;
  margin: 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #404040;
  border-top: 3px solid #18a058;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.status-text {
  color: #a0a0a0;
  font-size: 14px;
  margin-bottom: 20px;
  min-height: 20px;
}

.version-info {
  border-top: 1px solid #404040;
  padding-top: 20px;
  margin-top: 20px;
}

.version-info p {
  color: #a0a0a0;
  font-size: 12px;
  margin: 0;
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 新增动画效果 */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes floatingBubbles {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
    opacity: 0.9;
  }
  66% {
    transform: translateY(-10px) rotate(240deg);
    opacity: 0.8;
  }
}

@keyframes floatContainer {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}
</style> 