<template>
  <div class="activation-page">
    <div class="activation-container">
      <!-- 标题区域 -->
      <div class="content-area">
        <h1>🐠 小黄鱼监控工具</h1>
        <!-- 设备信息显示 -->
        <div class="device-info">
          <div class="info-label">设备指纹</div>
          <div class="device-id-display">
            <span style="color: #000">{{ encryptedDeviceId }}</span>
            <n-button
              size="small"
              type="primary"
              ghost
              @click="copyDeviceId"
              style="margin-left: 8px"
            >
              复制
            </n-button>
          </div>
          <p style="font-size: 12px; color: #a0a0a0; margin-top: 8px">
            请将此设备指纹提供给管理员以获取激活码（与后台打印的指纹一致）
          </p>
        </div>

        <!-- 激活码输入 -->
        <div class="activation-input">
          <div class="info-label">RSA激活码</div>
          <n-form ref="formRef" :model="formData">
            <n-form-item>
              <n-input
                v-model:value="formData.activationCode"
                type="textarea"
                placeholder="请输入RSA激活码"
                :rows="4"
                :disabled="activating"
              />
            </n-form-item>
          </n-form>
        </div>
      </div>

      <!-- 按钮区域 -->
      <div class="button-area">
        <div class="button-group">
          <n-button
            type="primary"
            size="large"
            @click="activateCode"
            :loading="activating"
            :disabled="!formData.activationCode.trim()"
            style="flex: 1"
          >
            {{ activating ? "验证中..." : "激活软件" }}
          </n-button>
          <n-button
            size="large"
            type="primary"
            ghost
            @click="closeWindow"
            style="margin-left: 12px"
          >
            关闭
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { useAppStore } from "../stores/app";
import { safeInvoke } from "../utils/tauri";
import { createDiscreteApi } from "naive-ui";

const { message } = createDiscreteApi(["message"]);

// 使用状态管理和路由
const router = useRouter();
const appStore = useAppStore();

// 响应式数据
const activating = ref(false);
const testing = ref(false);
const encryptedDeviceId = ref("");
const testResults = ref([]);

const formData = ref({
  activationCode: "",
});

const formRef = ref(null);

// 显示消息
const showMessage = (content, type = "info") => {
  console.log(`[${type.toUpperCase()}] ${content}`);
  message[type](content);
};

// 获取设备ID
const loadDeviceId = async () => {
  try {
    console.log("🔍 开始获取设备标识信息...");

    // 获取设备指纹
    const deviceFingerprint = await safeInvoke(
      "auth_generate_device_fingerprint"
    );
    encryptedDeviceId.value = deviceFingerprint;
    console.log("✅ 设备指纹:", deviceFingerprint);
  } catch (error) {
    console.error("获取设备ID失败:", error);
    showMessage("获取设备ID失败: " + error, "error");
  }
};

// 复制设备ID
const copyDeviceId = async () => {
  try {
    await navigator.clipboard.writeText(encryptedDeviceId.value);
    showMessage("设备指纹已复制到剪贴板", "success");
  } catch (error) {
    showMessage("复制失败", "error");
  }
};

// RSA激活码验证
const activateCode = async () => {
  try {
    activating.value = true;

    if (!formData.value.activationCode.trim()) {
      showMessage("请输入RSA激活码", "error");
      return;
    }

    showMessage("正在验证激活码...", "info");

    // 使用状态管理的激活方法
    const result = await appStore.activateApp(formData.value.activationCode);

    if (result.success) {
      showMessage("激活成功！", "success");
      // 激活成功后会自动跳转到主页面，不需要额外提示
    } else {
      showMessage(result.error || "激活失败", "error");
    }
  } catch (error) {
    console.error("激活失败:", error);
    showMessage(`激活失败: ${error}`, "error");
  } finally {
    activating.value = false;
  }
};

// 关闭窗口
const closeWindow = async () => {
  try {
    const currentWindow = getCurrentWebviewWindow();
    await currentWindow.close();
  } catch (error) {
    console.error("关闭窗口失败:", error);
  }
};

// 监听应用状态变化，激活成功后自动跳转
watch(
  () => appStore.appState,
  (newState) => {
    console.log("🔄 激活页面检测到状态变化:", newState);
    if (newState === "running" || newState === "activated") {
      console.log("✅ 激活成功，跳转到主页面");
      // 直接跳转，不显示重复消息
      setTimeout(() => {
        router.push("/");
      }, 500); // 缩短延迟时间
    }
  }
);

// 监听激活状态变化
watch(
  () => appStore.isActivated,
  (isActivated) => {
    console.log("🔄 激活页面检测到激活状态变化:", isActivated);
    if (isActivated) {
      console.log("✅ 激活状态为true，跳转到主页面");
      // 直接跳转，避免重复消息
      setTimeout(() => {
        router.push("/");
      }, 500);
    }
  }
);

// 页面加载
onMounted(async () => {
  await loadDeviceId();
});
</script>

<style scoped>
.activation-page {
  height: 100vh;
  /* 现代化渐变背景 - 激活主题色调 */
  background: linear-gradient(
    135deg,
    #1a1a2e 0%,
    #16213e 30%,
    #0f3460 60%,
    #1a1a2e 100%
  );
  background-size: 300% 300%;
  animation: activationGradient 10s ease infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
}

.activation-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 70%,
      rgba(34, 197, 94, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 30%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 50% 50%,
      rgba(168, 85, 247, 0.05) 0%,
      transparent 50%
    );
  animation: activationBubbles 15s ease-in-out infinite;
}

.activation-container {
  /* 暗主题容器样式 - 写死暗色主题 */
  background: rgba(24, 24, 28, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 10px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  width: 100%;
  max-width: 500px;
  height: 80vh;
  max-height: 600px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  animation: activationFloat 8s ease-in-out infinite;
}

.content-area {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  overflow-x: hidden;
}

.content-area h1 {
  text-align: center;
  color: #ffffff;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.content-area::-webkit-scrollbar {
  width: 6px;
}

.content-area::-webkit-scrollbar-track {
  background: #404040;
  border-radius: 3px;
}

.content-area::-webkit-scrollbar-thumb {
  background: #606060;
  border-radius: 3px;
}

.content-area::-webkit-scrollbar-thumb:hover {
  background: #808080;
}

.button-area {
  /* 暗主题按钮区域样式 - 写死暗色主题 */
  padding: 24px 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 0 0 20px 20px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2);
}

.info-label {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.device-info {
  /* 暗主题设备信息样式 - 写死暗色主题 */
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  margin-top: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.device-id-display {
  /* 暗主题设备ID显示样式 - 写死暗色主题 */
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  padding: 12px 16px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 12px;
  word-break: break-all;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

.activation-input {
  /* 暗主题激活输入区域样式 - 写死暗色主题 */
  margin-bottom: 24px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 暗主题输入框样式 - 写死暗色主题 */
.activation-input :deep(.n-input) {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  border-radius: 8px !important;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.activation-input :deep(.n-input__input-el) {
  background: transparent !important;
  color: #ffffff !important;
}

.activation-input :deep(.n-input--focus) {
  border-color: #18a058 !important;
  box-shadow: 0 0 0 2px rgba(24, 160, 88, 0.2) !important;
}

.activation-input :deep(.n-input__placeholder) {
  color: #a0a0a0 !important;
}

.button-group {
  display: flex;
  gap: 12px;
}

.test-section {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.test-section h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #ffffff;
}

/* 新增动画效果 */
@keyframes activationGradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes activationBubbles {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-15px) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-25px) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-10px) rotate(270deg);
    opacity: 0.7;
  }
}

@keyframes activationFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-8px) scale(1.01);
  }
}
</style>
