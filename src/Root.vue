<template>
  <div class="app-container">
    <n-dialog-provider>
      <n-message-provider>
        <router-view />
      </n-message-provider>
    </n-dialog-provider>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, provide } from "vue";
// eslint-disable-next-line no-unused-vars
import { NDialogProvider, NMessageProvider } from "naive-ui";
import { safeListen } from "./utils/tauri";
import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";
import { useAppState } from "./composables/useAppState";
import { useThemeStore } from "./stores/theme";

// 应用状态管理
const appState = useAppState();

// 主题管理
const themeStore = useThemeStore();

// 提供应用状态给子组件
Object.keys(appState).forEach((key) => {
  provide(key, appState[key]);
});

// 窗口关闭监听器
let unlistenWindowCloseRequested = null;

// 设置窗口关闭监听器（Windows兼容性）
const setupWindowCloseListener = async () => {
  try {
    unlistenWindowCloseRequested = await safeListen(
      "tauri://close-requested",
      async () => {
        console.log("🪟 Root页面收到窗口关闭请求，手动处理关闭");
        try {
          const currentWindow = getCurrentWebviewWindow();
          await currentWindow.close();
        } catch (error) {
          console.error("手动关闭窗口失败:", error);
          // 备用方案：强制关闭
          window.close();
        }
      }
    );
  } catch (error) {
    console.warn("设置窗口关闭监听器失败:", error);
  }
};

// 在mounted钩子中设置监听器
onMounted(async () => {
  // 初始化主题
  themeStore.initializeTheme();

  // 设置窗口关闭监听器
  await setupWindowCloseListener();

  // 初始化应用状态
  await appState.initialize();
});

// 组件卸载时清理
onUnmounted(() => {
  if (unlistenWindowCloseRequested) {
    unlistenWindowCloseRequested();
  }
});
</script>

<style>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  overflow: hidden;
  background-color: var(--n-base-color);
}

.app-container {
  height: 100vh;
  width: 100vw;
  background-color: var(--n-base-color);
}
</style>
