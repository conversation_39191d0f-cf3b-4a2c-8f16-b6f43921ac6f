import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { safeInvoke, safeListen } from '../utils/tauri'

export const useMonitorStore = defineStore('monitor', () => {
  // 监控状态
  const isRunning = ref(false)
  const isLoading = ref(false)
  const message = ref('监控已停止')
  const stats = ref({})
  const data = ref([])

  // 前端累计计数器 - 持久化存储
  const frontendStats = ref({
    totalFoundItems: 0, // 前端维护的累计商品数
    totalExecutionCount: 0, // 前端维护的累计执行次数
    totalKeywords: 0, // 前端维护的累计关键词数
    sessionStartTime: null // 会话开始时间
  })

  // 运行时间控制
  const startTime = ref(null)
  const pausedTime = ref(0) // 累计暂停时间
  const currentUptime = ref(0) // 当前运行时间（秒）
  let uptimeTimer = null

  // 配置状态 - 匹配后端 MonitorConfig 结构
  const config = ref({
    interval_seconds: 10,
    target_page_count: 5,
    keywords: [], // 后端必需字段
    exclude_keywords: [],
    min_price: 0.0,
    max_price: 99999.0,
    notify_enabled: true,
    keywords_price_rules: [], // 注意：后端字段名是 keyword_price_rules
    dingtalk_enabled: true, // 钉钉推送开关
    dingtalk_hooks: [],
    display_limit: 30,
    blocked_sellers: [], // 拉黑卖家列表
    selected_provinces: [] // 选中的省份列表
  })



  // 事件监听器
  let unlistenMonitorStatus = null
  let unlistenMonitorData = null
  let unlistenBatchData = null
  let unlistenConfigUpdate = null
  let unlistenMonitorError = null

  // 错误通知状态
  const errorNotification = ref({
    show: false,
    type: '',
    title: '',
    message: '',
    actions: []
  })

  // 持久化存储键名
  const STORAGE_KEY = 'goldfish_monitor_frontend_stats'

  // 加载前端统计数据
  function loadFrontendStats() {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const parsed = JSON.parse(stored)
        frontendStats.value = {
          totalFoundItems: parsed.totalFoundItems || 0,
          totalExecutionCount: parsed.totalExecutionCount || 0,
          totalKeywords: parsed.totalKeywords || 0,
          sessionStartTime: parsed.sessionStartTime || null
        }
      }
    } catch (error) {
      console.error('加载前端统计数据失败:', error)
      // 重置为默认值
      frontendStats.value = {
        totalFoundItems: 0,
        totalExecutionCount: 0,
        totalKeywords: 0,
        sessionStartTime: null
      }
    }
  }

  // 保存前端统计数据
  function saveFrontendStats() {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(frontendStats.value))
    } catch (error) {
      console.error('保存前端统计数据失败:', error)
    }
  }

  // 重置前端统计数据
  function resetFrontendStats() {
    frontendStats.value = {
      totalFoundItems: 0,
      totalExecutionCount: 0,
      totalKeywords: 0,
      sessionStartTime: Date.now()
    }
    saveFrontendStats()
  }

  // 计算属性
  const statusText = computed(() => {
    if (isLoading.value) {
      return isRunning.value ? '停止中...' : '启动中...'
    }
    return isRunning.value ? '监控运行中' : '监控已停止'
  })

  // 格式化运行时间
  const formattedUptime = computed(() => {
    const seconds = currentUptime.value
    if (seconds < 60) {
      return `${seconds}秒`
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}分${remainingSeconds}秒`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const remainingSeconds = seconds % 60
      return `${hours}小时${minutes}分${remainingSeconds}秒`
    }
  })

  // 启动运行时间计时器
  function startUptimeTimer() {
    if (uptimeTimer) {
      clearInterval(uptimeTimer)
    }

    startTime.value = Date.now()
    uptimeTimer = setInterval(() => {
      if (isRunning.value) {
        const now = Date.now()
        const elapsed = Math.floor((now - startTime.value) / 1000)
        currentUptime.value = elapsed
      }
    }, 1000)
  }

  // 停止运行时间计时器
  function stopUptimeTimer() {
    if (uptimeTimer) {
      clearInterval(uptimeTimer)
      uptimeTimer = null
    }
    currentUptime.value = 0
  }

  // 暂停运行时间计时器
  function pauseUptimeTimer() {
    if (uptimeTimer) {
      clearInterval(uptimeTimer)
      uptimeTimer = null
    }
  }



  // 监控相关函数
  async function checkStatus() {
    try {
      const status = await safeInvoke('monitor_get_status')
      if (status) {
        isRunning.value = status.is_running || false
        message.value = status.message || (isRunning.value ? '监控运行中' : '监控已停止')

        // 前端累计：每次手动刷新获取到数据就是一次数据接收
        frontendStats.value.totalExecutionCount += 1
        console.log(`📊 手动刷新-数据接收次数累计: +1, 总计: ${frontendStats.value.totalExecutionCount}`)
        saveFrontendStats()

        // 检查关键词数量是否变化，更新前端统计
        const newKeywords = status.total_keywords || 0
        if (newKeywords > frontendStats.value.totalKeywords) {
          frontendStats.value.totalKeywords = newKeywords
          console.log(`📊 手动刷新-关键词数量更新: ${frontendStats.value.totalKeywords}`)
          saveFrontendStats()
        }

        stats.value = {
          items_count: status.items_count || 0,
          total_found_items: status.total_found_items || 0,
          execution_count: status.execution_count || 0,
          total_keywords: status.total_keywords || 0,
          uptime_seconds: status.uptime_seconds || 0
        }
      } else {
        // 如果获取状态失败，回退到简单检查
        const running = await safeInvoke('app_is_running')
        isRunning.value = running || false
        message.value = isRunning.value ? '监控运行中' : '监控已停止'
      }
    } catch (error) {
      console.error('检查监控状态失败:', error)
      isRunning.value = false
      message.value = '监控已停止'
      stats.value = {}
    }
  }

  async function startMonitor() {
    try {
      isLoading.value = true
      await safeInvoke('app_start_monitoring')
      isRunning.value = true
      message.value = '监控运行中'
      // 启动运行时间计时器
      startUptimeTimer()
      return { success: true, message: '监控启动成功' }
    } catch (error) {
      console.error('启动监控失败:', error)
      return { success: false, message: `启动监控失败: ${error}` }
    } finally {
      isLoading.value = false
    }
  }

  async function stopMonitor() {
    try {
      isLoading.value = true
      await safeInvoke('app_stop_monitoring')
      isRunning.value = false
      message.value = '监控已停止'
      // 停止运行时间计时器
      stopUptimeTimer()
      return { success: true, message: '监控已停止' }
    } catch (error) {
      console.error('停止监控失败:', error)
      return { success: false, message: `停止监控失败: ${error}` }
    } finally {
      isLoading.value = false
    }
  }

  function clearData() {
    data.value = []
    // 同时重置前端累计统计
    resetFrontendStats()
  }

  // 错误处理函数
  function parseErrorType(message) {
    if (message.includes('需要登录') || message.includes('令牌过期') || message.includes('TOKEN_EXOIRED')) {
      return 'login_required'
    }
    if (message.includes('需要验证码') || message.includes('验证码') || message.includes('FAIL_SYS_USER_VALIDATE')) {
      return 'verification_required'
    }
    if (message.includes('网络') || message.includes('请求失败') || message.includes('连接')) {
      return 'network_error'
    }
    if (message.includes('激活') || message.includes('activation')) {
      return 'activation_error'
    }
    return 'general_error'
  }

  function showErrorNotification(errorMessage) {
    const errorType = parseErrorType(errorMessage)

    let title = '监控异常'
    let message = errorMessage
    let actions = []

    switch (errorType) {
      case 'login_required':
        title = '需要重新登录'
        message = '检测到登录令牌已过期，请重新登录以继续监控。'
        actions = [
          { label: '前往登录', type: 'primary', action: 'goto_login' },
          { label: '稍后处理', type: 'default', action: 'dismiss' }
        ]
        break
      case 'verification_required':
        title = '需要验证码验证'
        message = '系统检测到需要验证码验证，请完成验证后继续监控。'
        actions = [
          { label: '前往验证', type: 'primary', action: 'goto_verification' },
          { label: '稍后处理', type: 'default', action: 'dismiss' }
        ]
        break
      case 'network_error':
        title = '网络连接异常'
        message = '网络请求失败，请检查网络连接或稍后重试。'
        actions = [
          { label: '重试监控', type: 'primary', action: 'retry_monitor' },
          { label: '关闭', type: 'default', action: 'dismiss' }
        ]
        break
      case 'activation_error':
        title = '激活状态异常'
        message = '软件激活状态失效，请重新激活后使用。'
        actions = [
          { label: '前往激活', type: 'primary', action: 'goto_activation' },
          { label: '关闭', type: 'default', action: 'dismiss' }
        ]
        break
      default:
        title = '监控错误'
        message = errorMessage
        actions = [
          { label: '关闭', type: 'default', action: 'dismiss' }
        ]
    }

    errorNotification.value = {
      show: true,
      type: errorType,
      title,
      message,
      actions
    }
  }

  function hideErrorNotification() {
    errorNotification.value.show = false
  }

  // 配置相关函数
  async function loadConfig() {
    try {
      const configData = await safeInvoke('config_get')
      if (configData) {
        config.value = {
          interval_seconds: configData.interval_seconds || 10,
          target_page_count: configData.target_page_count || 5,
          keywords: configData.keywords || [],
          exclude_keywords: configData.exclude_keywords || [],
          min_price: configData.min_price || 0.0,
          max_price: configData.max_price || 99999.0,
          notify_enabled: configData.notify_enabled !== false,
          keywords_price_rules: configData.keyword_price_rules || [], // 注意后端字段名
          dingtalk_enabled: configData.dingtalk_enabled !== false, // 钉钉推送开关
          dingtalk_hooks: configData.dingtalk_hooks || [],
          display_limit: configData.display_limit || 30,
          blocked_sellers: configData.blocked_sellers || [], // 拉黑卖家列表
          selected_provinces: configData.selected_provinces || [] // 选中的省份列表
        }
      }
    } catch (error) {
      console.error('加载配置失败:', error)
      throw error
    }
  }

  async function saveConfig() {
    try {
      // 转换为后端期望的格式
      const backendConfig = {
        interval_seconds: config.value.interval_seconds,
        target_page_count: config.value.target_page_count,
        keywords: config.value.keywords,
        exclude_keywords: config.value.exclude_keywords,
        min_price: config.value.min_price,
        max_price: config.value.max_price,
        notify_enabled: config.value.notify_enabled,
        keyword_price_rules: config.value.keywords_price_rules, // 注意字段名转换
        dingtalk_enabled: config.value.dingtalk_enabled, // 钉钉推送开关
        dingtalk_hooks: config.value.dingtalk_hooks,
        display_limit: config.value.display_limit,
        blocked_sellers: config.value.blocked_sellers || [], // 拉黑卖家列表
        selected_provinces: config.value.selected_provinces || [] // 选中的省份列表
      }

      await safeInvoke('config_update', { config: backendConfig })
      return { success: true, message: '设置已保存' }
    } catch (error) {
      console.error('保存配置失败:', error)
      return { success: false, message: `保存设置失败: ${error}` }
    }
  }

  // 事件监听
  async function setupEventListeners() {
    try {
      // 监听监控状态变化
      unlistenMonitorStatus = await safeListen('monitor_status_changed', (event) => {
        const payload = event.payload
        console.log('📣 收到状态事件:', payload)

        const wasRunning = isRunning.value
        isRunning.value = payload.is_running
        message.value = payload.message || (payload.is_running ? '监控运行中' : '监控已停止')

        // 检查状态消息是否包含错误信息，如果包含则显示错误通知
        if (!payload.is_running && payload.message) {
          const errorKeywords = ['需要登录', '需要验证码', '令牌过期', '激活', '网络', '错误']
          const hasError = errorKeywords.some(keyword => payload.message.includes(keyword))
          if (hasError) {
            showErrorNotification(payload.message)
          }
        }

        // 根据状态变化控制计时器
        if (payload.is_running && !wasRunning) {
          // 从停止变为运行，启动计时器
          startUptimeTimer()
        } else if (!payload.is_running && wasRunning) {
          // 从运行变为停止，停止计时器
          stopUptimeTimer()
        }

        // 更新统计数据（不再使用后端的uptime_seconds）
        if (payload.items_count !== undefined || payload.total_found_items !== undefined) {
          // 前端累计：每次收到状态更新就是一次数据接收
          frontendStats.value.totalExecutionCount += 1
          console.log(`📊 数据接收次数累计: +1, 总计: ${frontendStats.value.totalExecutionCount}`)
          saveFrontendStats()

          // 检查关键词数量是否变化，更新前端统计
          const newKeywords = payload.total_keywords || 0
          if (newKeywords > frontendStats.value.totalKeywords) {
            frontendStats.value.totalKeywords = newKeywords
            console.log(`📊 关键词数量更新: ${frontendStats.value.totalKeywords}`)
            saveFrontendStats()
          }

          stats.value = {
            items_count: payload.items_count || 0,
            total_found_items: payload.total_found_items || 0,
            execution_count: payload.execution_count || 0,
            total_keywords: payload.total_keywords || 0
          }
        }
      })

      // 监听监控数据（单个商品）
      unlistenMonitorData = await safeListen('monitor_new_item', (event) => {
        const newItem = event.payload
        console.log('📦 收到新商品事件:', newItem)
        data.value.unshift(newItem)

        // 前端累计计数 +1（商品数）
        frontendStats.value.totalFoundItems += 1
        // 前端累计计数 +1（数据接收次数）
        frontendStats.value.totalExecutionCount += 1
        console.log(`📊 单个商品-数据接收次数累计: +1, 总计: ${frontendStats.value.totalExecutionCount}`)
        saveFrontendStats()

        // 保持数据量限制
        if (data.value.length > 1000) {
          data.value = data.value.slice(0, 500)
        }
      })

      // 监听批量监控数据
      unlistenBatchData = await safeListen('monitor_batch_items', (event) => {
        const batchItems = event.payload
        console.log('📦 收到批量商品事件:', batchItems.length, '个商品')

        // 批量添加到数据列表前端（已经按发布时间倒序排列）
        data.value.unshift(...batchItems)

        // 前端累计计数 + 批量数量（商品数）
        frontendStats.value.totalFoundItems += batchItems.length
        // 前端累计计数 +1（数据接收次数）
        frontendStats.value.totalExecutionCount += 1
        console.log(`📊 批量商品-数据接收次数累计: +1, 总计: ${frontendStats.value.totalExecutionCount}`)
        saveFrontendStats()

        // 保持数据量限制
        if (data.value.length > 300) {
          data.value = data.value.slice(0, 300)
        }
      })

      // 监听配置更新事件
      unlistenConfigUpdate = await safeListen('config_updated', (event) => {
        const updatedConfig = event.payload
        console.log('⚙️ 收到配置更新事件:', updatedConfig)

        // 更新本地配置状态
        config.value = {
          interval_seconds: updatedConfig.interval_seconds || 10,
          target_page_count: updatedConfig.target_page_count || 5,
          keywords: updatedConfig.keywords || [],
          exclude_keywords: updatedConfig.exclude_keywords || [],
          min_price: updatedConfig.min_price || 0.0,
          max_price: updatedConfig.max_price || 99999.0,
          notify_enabled: updatedConfig.notify_enabled !== false,
          keywords_price_rules: updatedConfig.keyword_price_rules || [], // 注意后端字段名
          dingtalk_enabled: updatedConfig.dingtalk_enabled !== false, // 钉钉推送开关
          dingtalk_hooks: updatedConfig.dingtalk_hooks || [],
          display_limit: updatedConfig.display_limit || 30,
          blocked_sellers: updatedConfig.blocked_sellers || [], // 拉黑卖家列表
          selected_provinces: updatedConfig.selected_provinces || [] // 选中的省份列表
        }
      })

      // 监听监控错误事件
      unlistenMonitorError = await safeListen('monitor_error', (event) => {
        const errorMessage = event.payload
        console.log('❌ 收到监控错误事件:', errorMessage)

        // 显示错误通知弹窗
        showErrorNotification(errorMessage)
      })

      console.log('✅ 监控事件监听器设置完成')
    } catch (error) {
      console.error('设置事件监听器失败:', error)
    }
  }

  function cleanup() {
    if (unlistenMonitorStatus) {
      unlistenMonitorStatus()
      unlistenMonitorStatus = null
    }
    if (unlistenMonitorData) {
      unlistenMonitorData()
      unlistenMonitorData = null
    }
    if (unlistenBatchData) {
      unlistenBatchData()
      unlistenBatchData = null
    }
    if (unlistenConfigUpdate) {
      unlistenConfigUpdate()
      unlistenConfigUpdate = null
    }
    if (unlistenMonitorError) {
      unlistenMonitorError()
      unlistenMonitorError = null
    }
    // 清理运行时间计时器
    if (uptimeTimer) {
      clearInterval(uptimeTimer)
      uptimeTimer = null
    }
  }

  // 防抖保存配置
  let saveTimeout = null
  async function debouncedSaveConfig() {
    if (saveTimeout) {
      clearTimeout(saveTimeout)
    }

    saveTimeout = setTimeout(async () => {
      try {
        const result = await saveConfig()
        if (result.success) {
          console.log('📝 配置已自动保存')
        } else {
          console.error('自动保存配置失败:', result.message)
        }
      } catch (error) {
        console.error('自动保存配置失败:', error)
      }
    }, 500)
  }

  // 初始化
  async function initialize() {
    try {
      // 加载前端统计数据
      loadFrontendStats()
      console.log('📊 前端统计数据已加载:', frontendStats.value)
      await checkStatus()
      await loadConfig()
      await setupEventListeners()
      console.log('✅ 监控 store 初始化完成')
    } catch (error) {
      console.error('监控 store 初始化失败:', error)
    }
  }

  return {
    // 状态
    isRunning,
    isLoading,
    message,
    stats,
    data,
    config,

    // 前端统计数据
    frontendStats,

    // 错误通知状态
    errorNotification,

    // 运行时间
    currentUptime,
    formattedUptime,

    // 计算属性
    statusText,

    // 监控方法
    checkStatus,
    startMonitor,
    stopMonitor,
    clearData,

    // 错误处理方法
    showErrorNotification,
    hideErrorNotification,

    // 配置方法
    loadConfig,
    saveConfig,
    debouncedSaveConfig,

    // 前端统计方法
    loadFrontendStats,
    saveFrontendStats,
    resetFrontendStats,

    // 生命周期方法
    initialize,
    cleanup
  }
})
