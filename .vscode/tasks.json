{"version": "2.0.0", "tasks": [{"type": "cargo", "command": "build", "args": ["--manifest-path=src-tauri/Cargo.toml"], "group": {"kind": "build", "isDefault": true}, "label": "cargo build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$rustc"]}, {"type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "label": "start frontend dev server", "isBackground": true, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": false, "clear": true}, "problemMatcher": [{"owner": "vite", "source": "vite", "severity": "error", "fileLocation": "relative", "pattern": {"regexp": "^(.*)$", "file": 1}, "background": {"activeOnStart": true, "beginsPattern": "^.*Local:.*$|^.*server started.*$", "endsPattern": "^.*Local:.*http://localhost:1430.*$|^.*ready in.*$|^.*Local.*1430.*$"}}]}, {"label": "build with frontend", "dependsOrder": "sequence", "dependsOn": ["start frontend dev server", "cargo build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"type": "shell", "command": "npm", "args": ["run", "tauri", "dev"], "group": "build", "label": "tauri dev (recommended)", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}, "problemMatcher": []}, {"type": "cargo", "command": "build", "args": ["--manifest-path=src-tauri/Cargo.toml", "--release"], "group": "build", "label": "cargo build --release", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$rustc"]}, {"type": "cargo", "command": "clean", "args": ["--manifest-path=src-tauri/Cargo.toml"], "group": "build", "label": "cargo clean", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "cargo-build-manual", "type": "shell", "command": "/Users/<USER>/.cargo/bin/cargo", "args": ["build", "--manifest-path=./src-tauri/Cargo.toml"], "group": "build", "presentation": {"echo": true, "reveal": "always", "panel": "new"}, "problemMatcher": ["$rustc"], "options": {"env": {"PATH": "/Users/<USER>/.cargo/bin:/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin"}}}, {"label": "cargo-dev", "type": "shell", "command": "/Users/<USER>/.cargo/bin/cargo", "args": ["tauri", "dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "panel": "new"}, "options": {"env": {"PATH": "/Users/<USER>/.cargo/bin:/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin", "RUST_BACKTRACE": "1", "RUST_LOG": "debug"}}}]}