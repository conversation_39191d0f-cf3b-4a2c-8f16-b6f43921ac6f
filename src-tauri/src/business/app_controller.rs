use crate::business::GoldfishBusiness;
use crate::services::account::AccountService;
use crate::services::{
    time::TimeService, AuthService, BrowserService, ConfigService, LoggingService, MonitorService,
    NotificationService,
};
use std::path::PathBuf;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};

/// 应用控制器 - 统一管理所有服务和业务逻辑
#[derive(Clone)]
pub struct AppController {
    app_handle: AppHandle,
    logging_service: LoggingService,
    config_service: ConfigService,
    auth_service: AuthService,
    browser_service: BrowserService,
    monitor_service: MonitorService,
    notification_service: NotificationService,
    account_service: AccountService,
    time_service: TimeService,
    goldfish_business: GoldfishBusiness,
}

impl AppController {
    /// 创建新的应用控制器
    pub async fn new(app_handle: AppHandle) -> Result<Self, String> {
        eprintln!("🎮 AppController::new 开始执行 (控制台输出)");

        // 获取应用目录
        let app_dir = app_handle
            .path()
            .app_data_dir()
            .unwrap_or_else(|_| PathBuf::from("./data"));
        eprintln!("📁 应用数据目录: {} (控制台输出)", app_dir.display());

        // 使用 Tauri 标准配置目录，确保在任何启动方式下都能正确工作
        let config_dir = app_handle.path().app_config_dir().unwrap_or_else(|_| {
            // 如果获取标准配置目录失败，使用应用数据目录下的config子目录
            app_handle
                .path()
                .app_data_dir()
                .unwrap_or_else(|_| PathBuf::from("./data"))
                .join("config")
        });
        eprintln!("📁 配置目录: {} (控制台输出)", config_dir.display());

        let log_dir = app_dir.join("logs");
        eprintln!("📁 日志目录: {} (控制台输出)", log_dir.display());

        // 初始化所有服务
        eprintln!("🔧 开始初始化日志服务 (控制台输出)");
        let logging_service = LoggingService::new(app_handle.clone());
        logging_service.initialize_log_file(log_dir).await?;
        eprintln!("✅ 日志服务初始化完成 (控制台输出)");

        let config_service = ConfigService::new(app_handle.clone(), config_dir.clone());
        config_service.initialize().await?;

        let auth_service = AuthService::new(app_handle.clone(), config_service.clone());
        let browser_service = BrowserService::new(app_handle.clone());
        let notification_service = NotificationService::new(app_handle.clone());
        let time_service = TimeService::new(app_handle.clone());

        // 初始化账号服务 - 使用应用数据目录存储会话数据
        let account_service = AccountService::new(
            app_handle.clone(),
            config_dir,
            app_dir.clone(),
            time_service.clone(),
        );
        account_service.initialize().await?;

        let monitor_service = MonitorService::new(
            app_handle.clone(),
            logging_service.clone(),
            config_service.clone(),
            notification_service.clone(),
            account_service.clone(),
        );

        // 创建业务处理器
        let goldfish_business = GoldfishBusiness::new(
            app_handle.clone(),
            logging_service.clone(),
            config_service.clone(),
            auth_service.clone(),
            browser_service.clone(),
            monitor_service.clone(),
            notification_service.clone(),
            account_service.clone(),
        );

        // 记录初始化完成日志
        logging_service
            .info("AppController", "new", "应用控制器初始化完成")
            .await?;

        Ok(Self {
            app_handle,
            logging_service,
            config_service,
            auth_service,
            browser_service,
            monitor_service,
            notification_service,
            account_service,
            time_service,
            goldfish_business,
        })
    }

    /// 获取日志服务
    pub fn logging_service(&self) -> &LoggingService {
        &self.logging_service
    }

    /// 获取配置服务
    pub fn config_service(&self) -> &ConfigService {
        &self.config_service
    }

    /// 获取认证服务
    pub fn auth_service(&self) -> &AuthService {
        &self.auth_service
    }

    /// 获取浏览器服务
    pub fn browser_service(&self) -> &BrowserService {
        &self.browser_service
    }

    /// 获取监控服务
    pub fn monitor_service(&self) -> &MonitorService {
        &self.monitor_service
    }

    /// 获取通知服务
    pub fn notification_service(&self) -> &NotificationService {
        &self.notification_service
    }

    /// 获取账号服务
    pub fn account_service(&self) -> &AccountService {
        &self.account_service
    }

    /// 获取时间服务
    pub fn time_service(&self) -> &TimeService {
        &self.time_service
    }

    /// 获取闲鱼业务处理器
    pub fn goldfish_business(&self) -> &GoldfishBusiness {
        &self.goldfish_business
    }

    /// 应用启动初始化
    pub async fn initialize_app(&self) -> Result<(), String> {
        self.logging_service
            .info("AppController", "initialize_app", "开始应用初始化")
            .await?;

        // 检查激活状态
        let is_activated = self
            .auth_service
            .check_activation_status()
            .await
            .unwrap_or(false);

        if is_activated {
            self.logging_service
                .info("AppController", "initialize_app", "应用已激活")
                .await?;
        } else {
            self.logging_service
                .warn("AppController", "initialize_app", "应用未激活")
                .await?;
        }

        // 加载配置
        let config = self.config_service.get_config().await;
        self.logging_service
            .info(
                "AppController",
                "initialize_app",
                &format!("配置加载完成，关键词数量: {}", config.get_keywords().len()),
            )
            .await?;

        self.logging_service
            .info("AppController", "initialize_app", "应用初始化完成")
            .await?;

        Ok(())
    }

    /// 应用关闭清理
    pub async fn cleanup_app(&self) -> Result<(), String> {
        self.logging_service
            .info("AppController", "cleanup_app", "开始应用清理")
            .await?;

        // 停止监控
        if self.monitor_service.is_running().await {
            self.monitor_service.stop_monitoring().await?;
        }

        // 关闭所有浏览器窗口
        self.browser_service.close_all_browsers().await?;

        self.logging_service
            .info("AppController", "cleanup_app", "应用清理完成")
            .await?;

        Ok(())
    }

    /// 获取应用状态
    pub async fn get_app_status(&self) -> Result<serde_json::Value, String> {
        let business_status = self.goldfish_business.get_business_status().await?;
        let device_info = self.auth_service.get_device_info().await?;
        let active_browsers = self.browser_service.get_active_browsers().await;

        Ok(serde_json::json!({
            "business": business_status,
            "device": device_info,
            "browsers": active_browsers,
            "timestamp": chrono::Utc::now().to_rfc3339()
        }))
    }
}
