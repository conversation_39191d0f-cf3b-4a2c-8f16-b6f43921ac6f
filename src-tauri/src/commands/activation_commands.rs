use serde_json;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, WebviewUrl, WebviewWindowBuilder};

use crate::services::{ConfigService, DeviceService, LogService, RsaActivationService};

/// 获取设备信息用于显示
#[tauri::command]
pub async fn get_device_info_for_display(app: AppHandle) -> Result<String, String> {
    let log_service = LogService::new(app.clone());
    let device_service = DeviceService::new(log_service);
    let device_info = device_service.get_device_info().await?;
    Ok(format!("{:?}", device_info))
}

/// 获取加密的设备ID
#[tauri::command]
pub async fn get_encrypted_device_id(app: AppHandle) -> Result<String, String> {
    let log_service = LogService::new(app.clone());
    let device_service = DeviceService::new(log_service);
    device_service.generate_encrypted_device_id().await
}

/// 验证RSA激活码
#[tauri::command]
pub async fn validate_activation_code(
    activation_code: String,
    app: AppHandle,
) -> Result<(), String> {
    // 创建服务实例
    let log_service = LogService::new(app.clone());
    let device_service = DeviceService::new(log_service.clone());
    let rsa_activation_service = RsaActivationService::new(log_service.clone(), device_service);
    let config_service = ConfigService::new(log_service.clone());

    // 验证激活码并获取激活信息
    let activation_info = rsa_activation_service
        .validate_rsa_activation_code(&activation_code)
        .await?;

    // 验证成功，保存激活信息到配置
    let mut config = config_service.get_config();
    config.rsa_activation_info = activation_info;

    // 保存配置
    config_service
        .save_config(&config)
        .await
        .map_err(|e| format!("保存激活信息失败: {}", e))?;

    println!("✅ RSA激活验证成功，激活信息已保存");

    // 激活成功后自动创建主窗口并关闭激活窗口
    // 1. 创建主窗口
    if let Err(e) = create_main_window(app.clone()).await {
        println!("⚠️ 创建主窗口失败: {}", e);
        // 即使创建主窗口失败，激活也是成功的，所以不返回错误
    } else {
        println!("✅ 主窗口创建成功");
    }

    // 2. 关闭激活窗口（延迟一点以确保主窗口创建完成）
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    if let Some(activation_window) = app.get_webview_window("activation") {
        if let Err(e) = activation_window.close() {
            println!("⚠️ 关闭激活窗口失败: {}", e);
        } else {
            println!("✅ 激活窗口已自动关闭");
        }
    }

    Ok(())
}

/// 检查激活状态
#[tauri::command]
pub async fn check_activation_status(app: AppHandle) -> Result<bool, String> {
    let log_service = LogService::new(app.clone());
    let config_service = ConfigService::new(log_service);

    let config = config_service.get_config();
    Ok(config.rsa_activation_info.activation_code.is_some())
}

/// 获取激活信息
#[tauri::command]
pub async fn get_activation_info(app: AppHandle) -> Result<serde_json::Value, String> {
    let log_service = LogService::new(app.clone());
    let config_service = ConfigService::new(log_service);

    let config = config_service.get_config();

    if config.rsa_activation_info.activation_code.is_some() {
        // 创建激活信息对象
        let activation_info = serde_json::json!({
            "activated": true,
            "device_name": config.rsa_activation_info.device_id.clone().unwrap_or_default(),
            "keywords": config.keywords,
            "hooks": config.dingtalk_hooks.len()
        });
        Ok(activation_info)
    } else {
        let activation_info = serde_json::json!({
            "activated": false,
            "device_name": "",
            "keywords": Vec::<String>::new(),
            "hooks": 0
        });
        Ok(activation_info)
    }
}

/// 测试设备指纹稳定性
#[tauri::command]
pub async fn test_device_fingerprint_stability(app: AppHandle) -> Result<(), String> {
    let log_service = LogService::new(app.clone());
    let device_service = DeviceService::new(log_service);
    let (is_stable, _) = device_service.verify_fingerprint_stability(5).await?;

    if is_stable {
        Ok(())
    } else {
        Err("设备指纹不稳定".to_string())
    }
}

/// 获取指纹详情
#[tauri::command]
pub async fn get_device_fingerprint_details(app: AppHandle) -> Result<String, String> {
    let log_service = LogService::new(app.clone());
    let device_service = DeviceService::new(log_service);
    device_service.get_fingerprint_details().await
}

/// 检查激活窗口是否存在
#[tauri::command]
pub async fn check_activation_window_exists(app: AppHandle) -> Result<bool, String> {
    if let Some(_) = app.get_webview_window("activation") {
        // 如果窗口存在，将其置于前台
        if let Some(window) = app.get_webview_window("activation") {
            window
                .set_focus()
                .map_err(|e| format!("无法聚焦窗口: {}", e))?;
        }
        Ok(true)
    } else {
        Ok(false)
    }
}

/// 创建激活窗口
#[tauri::command]
pub async fn create_activation_window(app: AppHandle) -> Result<(), String> {
    // 创建激活窗口，指向激活页面路由
    let _activation_window = WebviewWindowBuilder::new(
        &app,
        "activation",
        WebviewUrl::App("index.html#/activation".into()),
    )
    .title("设备激活")
    .inner_size(800.0, 600.0)
    .resizable(false)
    .center()
    .decorations(true)
    .closable(true)
    .skip_taskbar(false)
    .build()
    .map_err(|e| format!("创建激活窗口失败: {}", e))?;

    Ok(())
}

/// 检查主窗口是否存在
#[tauri::command]
pub async fn check_main_window_exists(app: AppHandle) -> Result<bool, String> {
    if let Some(_) = app.get_webview_window("main") {
        Ok(true)
    } else {
        Ok(false)
    }
}

/// 创建主窗口
#[tauri::command]
pub async fn create_main_window(app: AppHandle) -> Result<(), String> {
    // 创建主窗口，指向主页面路由
    let _main_window =
        WebviewWindowBuilder::new(&app, "main", WebviewUrl::App("index.html#/".into()))
            .title("Goldfish - 闲鱼商品监控")
            .inner_size(1200.0, 800.0)
            .min_inner_size(800.0, 600.0)
            .center()
            .decorations(true)
            .closable(true)
            .skip_taskbar(false)
            .build()
            .map_err(|e| format!("创建主窗口失败: {}", e))?;

    Ok(())
}

/// 检查Loading窗口是否存在
#[tauri::command]
pub async fn check_loading_window_exists(app: AppHandle) -> Result<bool, String> {
    if let Some(_) = app.get_webview_window("loading") {
        Ok(true)
    } else {
        Ok(false)
    }
}

/// 创建Loading窗口
#[tauri::command]
pub async fn create_loading_window(app: AppHandle) -> Result<(), String> {
    // 创建应用启动Loading窗口

    // 创建Loading窗口，指向加载页面路由
    let _loading_window = WebviewWindowBuilder::new(
        &app,
        "loading",
        WebviewUrl::App("index.html#/loading".into()),
    )
    .title("Goldfish - 启动中")
    .inner_size(600.0, 500.0)
    .resizable(false)
    .center()
    .decorations(true)
    .closable(true)
    .skip_taskbar(false)
    .always_on_top(true)
    .build()
    .map_err(|e| format!("创建Loading窗口失败: {}", e))?;

    Ok(())
}

/// 获取配置信息
#[tauri::command]
pub async fn get_config_info(app: AppHandle) -> Result<serde_json::Value, String> {
    let log_service = LogService::new(app.clone());
    let config_service = ConfigService::new(log_service);

    let config = config_service.get_config();

    let config_info = serde_json::json!({
        "device_name": config.rsa_activation_info.device_id.clone().unwrap_or_default(),
        "keywords": config.keywords,
        "hooks": config.dingtalk_hooks,
        "cookies": if config.login_cookie.is_none() || config.login_cookie.as_ref().unwrap().is_empty() { "未设置" } else { "已设置" }
    });
    Ok(config_info)
}
