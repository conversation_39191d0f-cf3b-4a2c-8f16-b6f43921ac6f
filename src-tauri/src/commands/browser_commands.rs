use crate::services::{<PERSON>rowserService, BrowserWindowConfig};
use tauri::{AppHandle, State};

/// 打开浏览器窗口
#[tauri::command]
pub async fn browser_open(
    _app: AppHandle, browser_service: State<'_, BrowserService>, target_url: String, _match_url_pattern: String, window_label: Option<String>, window_config: Option<BrowserWindowConfig>,
    allowed_domains: Option<Vec<String>>,
) -> Result<String, String> {
    if allowed_domains.is_some() {
        browser_service.open_browser_with_domains(&target_url, window_label, window_config, allowed_domains).await
    } else {
        browser_service.open_browser(&target_url, window_label, window_config).await
    }
}

/// 使用自定义配置打开浏览器窗口
#[tauri::command]
pub async fn browser_open_with_config(
    browser_service: State<'_, BrowserService>, target_url: String, _match_url_pattern: String, window_label: Option<String>, _callback_enabled: bool, window_config: Option<BrowserWindowConfig>,
    allowed_domains: Option<Vec<String>>,
) -> Result<String, String> {
    if allowed_domains.is_some() {
        browser_service.open_browser_with_domains(&target_url, window_label, window_config, allowed_domains).await
    } else {
        browser_service.open_browser(&target_url, window_label, window_config).await
    }
}

/// 关闭浏览器窗口
#[tauri::command]
pub async fn browser_close(browser_service: State<'_, BrowserService>, window_label: String) -> Result<(), String> {
    browser_service.close_browser(&window_label).await
}

/// 关闭所有浏览器窗口
#[tauri::command]
pub async fn browser_close_all(browser_service: State<'_, BrowserService>) -> Result<(), String> {
    browser_service.close_all_browsers().await
}

/// 获取活动的浏览器窗口列表
#[tauri::command]
pub async fn browser_get_active(browser_service: State<'_, BrowserService>) -> Result<Vec<String>, String> {
    Ok(browser_service.get_active_browsers().await)
}
