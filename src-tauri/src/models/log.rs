use chrono::Local;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LogEntry {
    pub id: String,
    pub timestamp: String,
    pub level: LogLevel,
    pub service: String,
    pub action: String,
    pub message: String,
    pub details: Option<serde_json::Value>,
    pub show_in_ui: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum LogLevel {
    Debug,
    Info,
    Warn,
    Error,
}

impl LogEntry {
    pub fn new(level: LogLevel, service: &str, action: &str, message: &str, show_in_ui: bool) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            timestamp: Local::now().format("%Y-%m-%d %H:%M:%S").to_string(),
            level,
            service: service.to_string(),
            action: action.to_string(),
            message: message.to_string(),
            details: None,
            show_in_ui,
        }
    }

    pub fn debug(service: &str, action: &str, message: &str) -> Self {
        Self::new(LogLevel::Debug, service, action, message, false)
    }

    pub fn info(service: &str, action: &str, message: &str) -> Self {
        Self::new(LogLevel::Info, service, action, message, true)
    }

    pub fn warn(service: &str, action: &str, message: &str) -> Self {
        Self::new(LogLevel::Warn, service, action, message, true)
    }

    pub fn error(service: &str, action: &str, message: &str) -> Self {
        Self::new(LogLevel::Error, service, action, message, true)
    }

    // 业务日志 - 用户可见的业务操作信息
    pub fn business_info(service: &str, action: &str, message: &str) -> Self {
        Self::new(LogLevel::Info, service, action, message, true)
    }

    pub fn business_warn(service: &str, action: &str, message: &str) -> Self {
        Self::new(LogLevel::Warn, service, action, message, true)
    }

    pub fn business_error(service: &str, action: &str, message: &str) -> Self {
        Self::new(LogLevel::Error, service, action, message, true)
    }

    // 技术调试日志 - 仅用于开发调试，不显示给用户
    pub fn tech_debug(service: &str, action: &str, message: &str) -> Self {
        Self::new(LogLevel::Debug, service, action, message, false)
    }

    pub fn tech_info(service: &str, action: &str, message: &str) -> Self {
        Self::new(LogLevel::Info, service, action, message, false)
    }
}

impl LogLevel {
    pub fn to_string(&self) -> &'static str {
        match self {
            LogLevel::Debug => "DEBUG",
            LogLevel::Info => "INFO",
            LogLevel::Warn => "WARN",
            LogLevel::Error => "ERROR",
        }
    }
}
