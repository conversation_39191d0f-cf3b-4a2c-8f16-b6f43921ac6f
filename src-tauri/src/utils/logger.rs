use crate::models::{LogEntry, LogLevel};

// 日志格式化工具
pub fn format_log_for_console(entry: &LogEntry) -> String {
    let level_prefix = match entry.level {
        LogLevel::Debug => "🔍",
        LogLevel::Info => "ℹ️",
        LogLevel::Warn => "⚠️",
        LogLevel::Error => "❌",
    };

    format!("{} [{}] {}::{} - {}", level_prefix, entry.timestamp, entry.service, entry.action, entry.message)
}
