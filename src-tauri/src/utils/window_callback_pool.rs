use std::collections::HashMap;
use std::sync::Arc;
use tauri::{WebviewWindow, WindowEvent};
use tokio::sync::Mutex;

/// 窗口事件回调返回结果
#[derive(Debug, Clone)]
pub enum CallbackResult {
    /// 继续默认行为
    Continue,
    /// 阻止默认行为（例如阻止窗口关闭）
    Prevent,
    /// 阻止默认行为并手动处理（例如异步处理后手动关闭窗口）
    PreventAndHandle,
    /// 执行回调并移除（用于一次性回调）
    ExecuteAndRemove,
}

/// 窗口事件回调配置
#[derive(Debug, Clone)]
pub struct CallbackConfig {
    /// 是否阻止默认关闭行为
    pub prevent_close: bool,
    /// 回调描述（用于调试）
    pub description: String,
    /// 是否只执行一次（执行后自动移除）
    pub execute_once: bool,
}

impl Default for CallbackConfig {
    fn default() -> Self {
        Self {
            prevent_close: false, // 默认不阻止关闭
            description: "未命名回调".to_string(),
            execute_once: false, // 默认可重复执行
        }
    }
}

/// 窗口事件回调类型
pub type WindowEventCallback = Arc<dyn Fn(&WebviewWindow, &WindowEvent) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<CallbackResult, String>> + Send>> + Send + Sync>;

/// 带配置的回调
#[derive(Clone)]
pub struct ConfiguredCallback {
    pub callback: WindowEventCallback,
    pub config: CallbackConfig,
}

/// 窗口回调池 - 管理所有窗口的事件回调
#[derive(Clone)]
pub struct WindowCallbackPool {
    /// 存储每个窗口标签对应的回调列表
    callbacks: Arc<Mutex<HashMap<String, Vec<ConfiguredCallback>>>>,
}

impl WindowCallbackPool {
    /// 创建新的回调池
    pub fn new() -> Self {
        Self { callbacks: Arc::new(Mutex::new(HashMap::new())) }
    }

    /// 为指定窗口添加事件回调
    pub async fn add_callback<F>(&self, window_label: &str, callback: F, config: CallbackConfig)
    where
        F: Fn(&WebviewWindow, &WindowEvent) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<CallbackResult, String>> + Send>> + Send + Sync + 'static,
    {
        let mut callbacks = self.callbacks.lock().await;
        let window_callbacks = callbacks.entry(window_label.to_string()).or_insert_with(Vec::new);
        window_callbacks.push(ConfiguredCallback { callback: Arc::new(callback), config });
    }

    /// 为指定窗口添加事件回调（使用默认配置）
    pub async fn add_callback_default<F>(&self, window_label: &str, callback: F)
    where
        F: Fn(&WebviewWindow, &WindowEvent) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<CallbackResult, String>> + Send>> + Send + Sync + 'static,
    {
        self.add_callback(window_label, callback, CallbackConfig::default()).await;
    }

    /// 移除指定窗口的所有回调
    pub async fn remove_window_callbacks(&self, window_label: &str) {
        let mut callbacks = self.callbacks.lock().await;
        callbacks.remove(window_label);
    }

    /// 执行指定窗口的所有回调，返回是否应该阻止默认行为
    pub async fn execute_callbacks(&self, window: &WebviewWindow, event: &WindowEvent) -> Result<CallbackResult, String> {
        let mut callbacks = self.callbacks.lock().await;

        if let Some(window_callbacks) = callbacks.get_mut(window.label()) {
            let mut should_prevent = false;
            let mut callbacks_to_remove = Vec::new();

            for (index, configured_callback) in window_callbacks.iter().enumerate() {
                // 检查是否是关闭请求事件，且回调配置要求阻止关闭
                let is_close_request = matches!(event, WindowEvent::CloseRequested { .. });
                let should_prevent_close = configured_callback.config.prevent_close && is_close_request;

                if should_prevent_close {
                    should_prevent = true;
                }

                match (configured_callback.callback)(window, event).await {
                    Ok(CallbackResult::Prevent) | Ok(CallbackResult::PreventAndHandle) => {
                        // 回调本身也可以要求阻止默认行为
                        should_prevent = true;
                    }
                    Ok(CallbackResult::ExecuteAndRemove) => {
                        // 执行回调并移除
                        callbacks_to_remove.push(index);
                    }
                    Ok(CallbackResult::Continue) => {
                        // 继续执行下一个回调
                    }
                    Err(e) => {
                        eprintln!("窗口回调 '{}' 执行失败: {}", configured_callback.config.description, e);
                        // 继续执行其他回调，不因为一个失败而停止
                    }
                }

                // 如果是一次性执行的回调，标记为需要移除
                if configured_callback.config.execute_once {
                    callbacks_to_remove.push(index);
                }
            }

            // 移除一次性执行的回调（从后往前移除，避免索引变化）
            for &index in callbacks_to_remove.iter().rev() {
                window_callbacks.remove(index);
            }

            if should_prevent {
                return Ok(CallbackResult::PreventAndHandle);
            }
        }

        // 如果没有回调或所有回调都不要求阻止，则继续默认行为
        Ok(CallbackResult::Continue)
    }

    /// 获取指定窗口的回调数量
    pub async fn get_callback_count(&self, window_label: &str) -> usize {
        let callbacks = self.callbacks.lock().await;
        callbacks.get(window_label).map(|v| v.len()).unwrap_or(0)
    }

    /// 同步检查指定窗口是否有回调要求阻止关闭
    pub fn should_prevent_close_sync(&self, window_label: &str) -> bool {
        if let Ok(callbacks) = self.callbacks.try_lock() {
            if let Some(window_callbacks) = callbacks.get(window_label) {
                return window_callbacks.iter().any(|configured_callback| configured_callback.config.prevent_close);
            }
        }
        false
    }

    /// 清空所有回调
    pub async fn clear_all(&self) {
        let mut callbacks = self.callbacks.lock().await;
        callbacks.clear();
    }
}

impl Default for WindowCallbackPool {
    fn default() -> Self {
        Self::new()
    }
}

/// 全局窗口回调池实例
static WINDOW_CALLBACK_POOL: once_cell::sync::Lazy<WindowCallbackPool> = once_cell::sync::Lazy::new(|| WindowCallbackPool::new());

/// 获取全局窗口回调池实例
pub fn get_window_callback_pool() -> &'static WindowCallbackPool {
    &WINDOW_CALLBACK_POOL
}

/// 便捷函数：为窗口添加关闭请求回调
pub async fn add_close_requested_callback<F>(window_label: &str, callback: F, prevent_close: bool, description: &str)
where
    F: Fn(&WebviewWindow) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<CallbackResult, String>> + Send>> + Send + Sync + 'static,
{
    let pool = get_window_callback_pool();
    let config = CallbackConfig {
        prevent_close,
        description: description.to_string(),
        execute_once: true, // 关闭请求回调默认只执行一次
    };

    pool.add_callback(
        window_label,
        move |window, event| {
            if matches!(event, WindowEvent::CloseRequested { .. }) {
                // 执行用户回调，然后将结果转换为ExecuteAndRemove
                let user_callback = callback(window);
                Box::pin(async move {
                    match user_callback.await {
                        Ok(_) => Ok(CallbackResult::ExecuteAndRemove),
                        Err(e) => Err(e),
                    }
                })
            } else {
                // 对于非CloseRequested事件，返回Continue但不移除回调
                Box::pin(async { Ok(CallbackResult::Continue) })
            }
        },
        CallbackConfig {
            prevent_close,
            description: description.to_string(),
            execute_once: false, // 改为false，让回调在正确的事件时才被移除
        },
    )
    .await;
}

/// 便捷函数：为窗口添加关闭请求回调（默认不阻止关闭）
pub async fn add_close_requested_callback_default<F>(window_label: &str, callback: F)
where
    F: Fn(&WebviewWindow) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<CallbackResult, String>> + Send>> + Send + Sync + 'static,
{
    add_close_requested_callback(window_label, callback, false, "默认关闭回调").await;
}

/// 便捷函数：为窗口添加已关闭回调
pub async fn add_closed_callback<F>(window_label: &str, callback: F, description: &str)
where
    F: Fn(&WebviewWindow) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<CallbackResult, String>> + Send>> + Send + Sync + 'static,
{
    let pool = get_window_callback_pool();
    let config = CallbackConfig {
        prevent_close: false, // 关闭后的回调不需要阻止关闭
        description: description.to_string(),
        execute_once: false, // 关闭后的回调可以重复执行
    };

    pool.add_callback(
        window_label,
        move |window, event| {
            if matches!(event, WindowEvent::Destroyed) {
                callback(window)
            } else {
                Box::pin(async { Ok(CallbackResult::Continue) })
            }
        },
        config,
    )
    .await;
}

/// 便捷函数：移除窗口的所有回调
pub async fn remove_window_callbacks(window_label: &str) {
    let pool = get_window_callback_pool();
    pool.remove_window_callbacks(window_label).await;
}
