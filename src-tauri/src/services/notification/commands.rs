use super::service::{NotificationChannel, NotificationMessage, NotificationService};
use std::collections::HashMap;
use tauri::State;

/// 添加通知渠道
#[tauri::command]
pub async fn notification_add_channel(notification_service: State<'_, NotificationService>, name: String, channel: NotificationChannel) -> Result<(), String> {
    notification_service.add_channel(name, channel).await
}

/// 发送通知
#[tauri::command]
pub async fn notification_send(notification_service: State<'_, NotificationService>, channel_name: String, message: NotificationMessage) -> Result<(), String> {
    notification_service.send_notification(&channel_name, message).await
}

/// 广播通知
#[tauri::command]
pub async fn notification_broadcast(notification_service: State<'_, NotificationService>, message: NotificationMessage) -> Result<(), String> {
    notification_service.broadcast_notification(message).await
}

/// 测试通知渠道
#[tauri::command]
pub async fn notification_test_channel(notification_service: State<'_, NotificationService>, channel_name: String) -> Result<(), String> {
    notification_service.test_channel(&channel_name).await
}

/// 获取所有通知渠道
#[tauri::command]
pub async fn notification_get_channels(notification_service: State<'_, NotificationService>) -> Result<HashMap<String, NotificationChannel>, String> {
    Ok(notification_service.get_channels().await)
}

/// 获取钉钉推送状态
#[tauri::command]
pub async fn notification_get_dingtalk_status(notification_service: State<'_, NotificationService>, hook_urls: Vec<String>) -> Result<Vec<(String, usize, usize)>, String> {
    Ok(notification_service.get_dingtalk_push_status(&hook_urls).await)
}

/// 重置钉钉推送频率限制
#[tauri::command]
pub async fn notification_reset_dingtalk_limits(notification_service: State<'_, NotificationService>) -> Result<(), String> {
    notification_service.reset_dingtalk_rate_limits().await;
    Ok(())
}

/// 移除通知渠道
#[tauri::command]
pub async fn notification_remove_channel(notification_service: State<'_, NotificationService>, name: String) -> Result<(), String> {
    notification_service.remove_channel(&name).await
}

/// 测试所有钉钉推送Hook
#[tauri::command]
pub async fn test_all_dingtalk_hooks(notification_service: State<'_, NotificationService>, hook_urls: Vec<String>) -> Result<Vec<(String, String)>, String> {
    notification_service.test_all_dingtalk_hooks(&hook_urls).await
}
