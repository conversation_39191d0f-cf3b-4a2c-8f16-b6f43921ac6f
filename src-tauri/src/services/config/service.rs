use crate::models::config::MonitorConfig;
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;
use std::sync::Arc;
use tauri::{App<PERSON>andle, Emitter, Manager};
use tokio::sync::RwLock;

/// 配置文件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigFileInfo {
    pub path: String,
    pub exists: bool,
    pub size: Option<u64>,
    pub modified: Option<String>,
}

/// 纯粹的配置服务 - 完全解耦，只处理配置文件的读写
#[derive(Clone)]
pub struct ConfigService {
    app_handle: AppHandle,
    config: Arc<RwLock<MonitorConfig>>,
    config_file_path: PathBuf,
}

impl ConfigService {
    /// 创建新的配置服务实例
    pub fn new(app_handle: AppHandle, config_dir: PathBuf) -> Self {
        let config_file_path = config_dir.join("config.json");

        Self {
            app_handle,
            config: Arc::new(RwLock::new(MonitorConfig::default())),
            config_file_path,
        }
    }

    /// 获取app_handle的引用
    pub fn app_handle(&self) -> &AppHandle {
        &self.app_handle
    }

    /// 初始化配置服务
    pub async fn initialize(&self) -> Result<(), String> {
        // 确保配置目录存在
        if let Some(parent) = self.config_file_path.parent() {
            fs::create_dir_all(parent).map_err(|e| format!("创建配置目录失败: {}", e))?;
        }

        // 加载配置
        self.load_config().await?;

        Ok(())
    }

    /// 加载配置文件
    pub async fn load_config(&self) -> Result<MonitorConfig, String> {
        let config = if self.config_file_path.exists() {
            // 尝试读取现有配置文件
            match fs::read_to_string(&self.config_file_path) {
                Ok(content) => {
                    // 尝试解析配置文件
                    match serde_json::from_str::<MonitorConfig>(&content) {
                        Ok(parsed_config) => {
                            // 解析成功，验证配置
                            match parsed_config.validate() {
                                Ok(_) => parsed_config,
                                Err(e) => {
                                    // 配置验证失败，使用默认配置并重新保存
                                    eprintln!("⚠️  配置文件验证失败: {}, 使用默认配置", e);
                                    let default_config = MonitorConfig::default();
                                    self.save_config(&default_config).await?;
                                    default_config
                                }
                            }
                        }
                        Err(e) => {
                            // 解析失败，使用默认配置并重新保存
                            eprintln!("⚠️  配置文件格式错误: {}, 使用默认配置", e);
                            let default_config = MonitorConfig::default();
                            self.save_config(&default_config).await?;
                            default_config
                        }
                    }
                }
                Err(e) => {
                    // 读取失败，使用默认配置并重新保存
                    eprintln!("⚠️  读取配置文件失败: {}, 使用默认配置", e);
                    let default_config = MonitorConfig::default();
                    self.save_config(&default_config).await?;
                    default_config
                }
            }
        } else {
            // 配置文件不存在，创建默认配置
            // 创建默认配置
            let default_config = MonitorConfig::default();
            self.save_config(&default_config).await?;
            default_config
        };

        // 更新内存中的配置
        {
            let mut current_config = self.config.write().await;
            *current_config = config.clone();
        }

        // 发送配置更新事件
        self.emit_config_updated(&config).await?;

        Ok(config)
    }

    /// 保存配置文件
    pub async fn save_config(&self, config: &MonitorConfig) -> Result<(), String> {
        // 验证配置
        config.validate()?;

        // 序列化配置
        let content =
            serde_json::to_string_pretty(config).map_err(|e| format!("序列化配置失败: {}", e))?;

        // 写入文件
        fs::write(&self.config_file_path, content)
            .map_err(|e| format!("写入配置文件失败: {}", e))?;

        // 更新内存中的配置
        {
            let mut current_config = self.config.write().await;
            *current_config = config.clone();
        }

        // 发送配置更新事件
        self.emit_config_updated(config).await?;

        Ok(())
    }

    /// 获取当前配置
    pub async fn get_config(&self) -> MonitorConfig {
        let config = self.config.read().await;
        config.clone()
    }

    /// 更新配置
    pub async fn update_config(&self, mut new_config: MonitorConfig) -> Result<(), String> {
        // 保留原有的激活信息
        let current_config = self.get_config().await;
        new_config.rsa_activation_info = current_config.rsa_activation_info;

        self.save_config(&new_config).await
    }

    /// 重新加载配置文件
    pub async fn reload_config(&self) -> Result<MonitorConfig, String> {
        self.load_config().await
    }

    /// 获取配置文件路径信息
    pub fn get_config_file_info(&self) -> ConfigFileInfo {
        let path = self.config_file_path.to_string_lossy().to_string();
        let exists = self.config_file_path.exists();

        let (size, modified) = if exists {
            if let Ok(metadata) = fs::metadata(&self.config_file_path) {
                let size = Some(metadata.len());
                let modified = metadata.modified().ok().and_then(|time| {
                    let datetime: chrono::DateTime<chrono::Utc> = time.into();
                    Some(datetime.format("%Y-%m-%d %H:%M:%S").to_string())
                });
                (size, modified)
            } else {
                (None, None)
            }
        } else {
            (None, None)
        };

        ConfigFileInfo {
            path,
            exists,
            size,
            modified,
        }
    }

    /// 备份配置文件
    pub async fn backup_config(&self) -> Result<String, String> {
        if !self.config_file_path.exists() {
            return Err("配置文件不存在".to_string());
        }

        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
        let backup_path = self
            .config_file_path
            .with_extension(format!("json.backup.{}", timestamp));

        fs::copy(&self.config_file_path, &backup_path)
            .map_err(|e| format!("备份配置文件失败: {}", e))?;

        Ok(backup_path.to_string_lossy().to_string())
    }

    /// 从备份恢复配置
    pub async fn restore_config(&self, backup_path: &str) -> Result<(), String> {
        let backup_path = PathBuf::from(backup_path);

        if !backup_path.exists() {
            return Err("备份文件不存在".to_string());
        }

        // 验证备份文件格式
        let content =
            fs::read_to_string(&backup_path).map_err(|e| format!("读取备份文件失败: {}", e))?;

        let config: MonitorConfig =
            serde_json::from_str(&content).map_err(|e| format!("解析备份文件失败: {}", e))?;

        // 验证配置
        config.validate()?;

        // 恢复配置
        self.save_config(&config).await?;

        Ok(())
    }

    /// 重置为默认配置
    pub async fn reset_to_default(&self) -> Result<(), String> {
        let default_config = MonitorConfig::default();
        self.save_config(&default_config).await
    }

    /// 验证配置文件格式
    pub async fn validate_config_file(&self) -> Result<bool, String> {
        if !self.config_file_path.exists() {
            return Ok(false);
        }

        let content = fs::read_to_string(&self.config_file_path)
            .map_err(|e| format!("读取配置文件失败: {}", e))?;

        match serde_json::from_str::<MonitorConfig>(&content) {
            Ok(config) => {
                config.validate()?;
                Ok(true)
            }
            Err(e) => Err(format!("配置文件格式无效: {}", e)),
        }
    }

    /// 获取配置文件路径
    pub fn get_config_file_path(&self) -> &PathBuf {
        &self.config_file_path
    }

    /// 发送配置更新事件
    async fn emit_config_updated(&self, config: &MonitorConfig) -> Result<(), String> {
        self.app_handle
            .emit("config_updated", config)
            .map_err(|e| format!("发送配置更新事件失败: {}", e))
    }

    /// 发送配置错误事件
    async fn emit_config_error(&self, error: &str) -> Result<(), String> {
        self.app_handle
            .emit("config_error", error)
            .map_err(|e| format!("发送配置错误事件失败: {}", e))
    }
}

/// 静态初始化方法（兼容性）
impl ConfigService {
    pub async fn init() -> Result<(), String> {
        // 这是一个兼容性方法，实际初始化在new时完成
        Ok(())
    }

    pub fn with_app_handle(
        _logging_service: crate::services::logging::service::LoggingService,
        app_handle: AppHandle,
    ) -> Self {
        // 获取配置目录，使用与 AppController 相同的逻辑
        let config_dir = app_handle.path().app_config_dir().unwrap_or_else(|_| {
            // 如果获取标准配置目录失败，使用应用数据目录下的config子目录
            app_handle
                .path()
                .app_data_dir()
                .unwrap_or_else(|_| PathBuf::from("./data"))
                .join("config")
        });

        Self::new(app_handle, config_dir)
    }
}
