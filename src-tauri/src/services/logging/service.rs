use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::fs::{File, OpenOptions};
use std::io::{BufWriter, Write};
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use tauri::{AppHandle, Emitter};
use tokio::sync::RwLock;

/// 日志级别
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum LogLevel {
    Debug,
    Info,
    Warn,
    Error,
    TechDebug,
    TechInfo,
    BusinessInfo,
    BusinessWarn,
    BusinessError,
}

impl LogLevel {
    pub fn to_string(&self) -> &'static str {
        match self {
            LogLevel::Debug => "DEBUG",
            LogLevel::Info => "INFO",
            LogLevel::Warn => "WARN",
            LogLevel::Error => "ERROR",
            LogLevel::TechDebug => "TECH_DEBUG",
            LogLevel::TechInfo => "TECH_INFO",
            LogLevel::BusinessInfo => "BUSINESS_INFO",
            LogLevel::BusinessWarn => "BUSINESS_WARN",
            LogLevel::BusinessError => "BUSINESS_ERROR",
        }
    }
}

/// 日志条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub id: String,
    pub timestamp: String,
    pub level: LogLevel,
    pub module: String,
    pub function: String,
    pub message: String,
    pub details: Option<String>,
}

impl LogEntry {
    pub fn new(level: LogLevel, module: &str, function: &str, message: &str) -> Self {
        let now = Utc::now();
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            timestamp: now.format("%Y-%m-%d %H:%M:%S%.3f").to_string(),
            level,
            module: module.to_string(),
            function: function.to_string(),
            message: message.to_string(),
            details: None,
        }
    }

    pub fn with_details(mut self, details: &str) -> Self {
        self.details = Some(details.to_string());
        self
    }
}

/// 日志文件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogFileInfo {
    pub path: String,
    pub size: u64,
    pub created: String,
    pub modified: String,
}

/// 纯粹的日志服务 - 完全解耦，不包含任何业务逻辑
#[derive(Clone)]
pub struct LoggingService {
    app_handle: AppHandle,
    memory_logs: Arc<RwLock<VecDeque<LogEntry>>>,
    file_writer: Arc<Mutex<Option<BufWriter<File>>>>,
    log_file_path: Arc<Mutex<Option<PathBuf>>>,
    max_memory_logs: usize,
}

impl LoggingService {
    /// 创建新的日志服务实例
    pub fn new(app_handle: AppHandle) -> Self {
        Self { app_handle, memory_logs: Arc::new(RwLock::new(VecDeque::new())), file_writer: Arc::new(Mutex::new(None)), log_file_path: Arc::new(Mutex::new(None)), max_memory_logs: 1000 }
    }

    /// 获取app_handle的引用
    pub fn app_handle(&self) -> &AppHandle {
        &self.app_handle
    }

    /// 初始化日志文件
    pub async fn initialize_log_file(&self, log_dir: PathBuf) -> Result<(), String> {
        let log_file_path = log_dir.join("goldfish_app.log");

        // 确保目录存在
        if let Some(parent) = log_file_path.parent() {
            std::fs::create_dir_all(parent).map_err(|e| format!("创建日志目录失败: {}", e))?;
        }

        // 打开或创建日志文件
        let file = OpenOptions::new().create(true).append(true).open(&log_file_path).map_err(|e| format!("打开日志文件失败: {}", e))?;

        let writer = BufWriter::new(file);

        // 更新文件写入器和路径
        {
            let mut file_writer = self.file_writer.lock().unwrap();
            *file_writer = Some(writer);
        }

        {
            let mut path = self.log_file_path.lock().unwrap();
            *path = Some(log_file_path);
        }

        self.log(LogLevel::Info, "LoggingService", "initialize_log_file", "日志文件初始化完成").await?;

        Ok(())
    }

    /// 记录日志
    pub async fn log(&self, level: LogLevel, module: &str, function: &str, message: &str) -> Result<(), String> {
        let entry = LogEntry::new(level, module, function, message);

        // 格式化日志输出，包含时间戳和详细信息
        let timestamp = chrono::Local::now().format("%H:%M:%S");

        // 根据日志级别设置颜色和图标
        let (color_code, level_icon, level_text) = match entry.level {
            LogLevel::Debug => ("\x1b[36m", "🔍", "DEBUG"),         // 青色
            LogLevel::Info => ("\x1b[32m", "ℹ️", "INFO "),          // 绿色
            LogLevel::Warn => ("\x1b[33m", "⚠️", "WARN "),          // 黄色
            LogLevel::Error => ("\x1b[31m", "❌", "ERROR"),         // 红色
            LogLevel::TechDebug => ("\x1b[35m", "🔧", "TECH "),     // 紫色
            LogLevel::TechInfo => ("\x1b[34m", "🛠️", "TECH "),      // 蓝色
            LogLevel::BusinessInfo => ("\x1b[92m", "💼", "BIZ  "),  // 亮绿色
            LogLevel::BusinessWarn => ("\x1b[93m", "⚡", "BIZ  "),  // 亮黄色
            LogLevel::BusinessError => ("\x1b[91m", "💥", "BIZ  "), // 亮红色
        };

        // 重置颜色代码
        let reset_color = "\x1b[0m";

        // 限制模块和函数名的总长度
        let module_function = format!("{}.{}", entry.module, entry.function);
        let truncated_module_function = if module_function.len() > 35 { format!("{}...", &module_function[..32]) } else { module_function };

        // 格式化输出，保证左边对齐
        println!("{}{} | {:<35} | {}{}", color_code, timestamp, truncated_module_function, entry.message, reset_color);

        // 添加到内存日志
        {
            let mut memory_logs = self.memory_logs.write().await;
            memory_logs.push_back(entry.clone());

            // 保持内存日志数量限制
            while memory_logs.len() > self.max_memory_logs {
                memory_logs.pop_front();
            }
        }

        // 写入文件
        self.write_to_file(&entry).await?;

        // 发送事件到前端
        if let Err(e) = self.app_handle.emit("new_log_entry", &entry) {
            eprintln!("发送日志事件失败: {}", e);
        }

        Ok(())
    }

    /// 便捷方法 - Debug级别
    pub async fn debug(&self, module: &str, function: &str, message: &str) -> Result<(), String> {
        self.log(LogLevel::Debug, module, function, message).await
    }

    /// 便捷方法 - Info级别
    pub async fn info(&self, module: &str, function: &str, message: &str) -> Result<(), String> {
        self.log(LogLevel::Info, module, function, message).await
    }

    /// 便捷方法 - Warn级别
    pub async fn warn(&self, module: &str, function: &str, message: &str) -> Result<(), String> {
        self.log(LogLevel::Warn, module, function, message).await
    }

    /// 便捷方法 - Error级别
    pub async fn error(&self, module: &str, function: &str, message: &str) -> Result<(), String> {
        self.log(LogLevel::Error, module, function, message).await
    }

    /// 便捷方法 - 技术调试
    pub async fn tech_debug(&self, module: &str, function: &str, message: &str) -> Result<(), String> {
        self.log(LogLevel::TechDebug, module, function, message).await
    }

    /// 便捷方法 - 技术信息
    pub async fn tech_info(&self, module: &str, function: &str, message: &str) -> Result<(), String> {
        self.log(LogLevel::TechInfo, module, function, message).await
    }

    /// 便捷方法 - 业务信息
    pub async fn business_info(&self, module: &str, function: &str, message: &str) -> Result<(), String> {
        self.log(LogLevel::BusinessInfo, module, function, message).await
    }

    /// 便捷方法 - 业务警告
    pub async fn business_warn(&self, module: &str, function: &str, message: &str) -> Result<(), String> {
        self.log(LogLevel::BusinessWarn, module, function, message).await
    }

    /// 便捷方法 - 业务错误
    pub async fn business_error(&self, module: &str, function: &str, message: &str) -> Result<(), String> {
        self.log(LogLevel::BusinessError, module, function, message).await
    }

    /// 便捷方法 - 业务成功
    pub async fn business_success(&self, module: &str, function: &str, message: &str) -> Result<(), String> {
        self.log(LogLevel::BusinessInfo, module, function, &format!("✅ {}", message)).await
    }

    /// 获取内存中的日志
    pub async fn get_logs(&self, limit: Option<usize>) -> Vec<LogEntry> {
        let memory_logs = self.memory_logs.read().await;
        let logs: Vec<LogEntry> = memory_logs.iter().cloned().collect();

        if let Some(limit) = limit {
            logs.into_iter().rev().take(limit).rev().collect()
        } else {
            logs
        }
    }

    /// 清空内存日志
    pub async fn clear_logs(&self) -> Result<(), String> {
        let mut memory_logs = self.memory_logs.write().await;
        memory_logs.clear();

        self.log(LogLevel::Info, "LoggingService", "clear_logs", "内存日志已清空").await?;
        Ok(())
    }

    /// 获取日志文件信息
    pub async fn get_log_file_info(&self) -> Result<LogFileInfo, String> {
        let path_guard = self.log_file_path.lock().unwrap();
        let log_path = path_guard.as_ref().ok_or("日志文件未初始化")?;

        let metadata = std::fs::metadata(log_path).map_err(|e| format!("获取日志文件信息失败: {}", e))?;

        let created = metadata.created().map_err(|e| format!("获取创建时间失败: {}", e))?;
        let modified = metadata.modified().map_err(|e| format!("获取修改时间失败: {}", e))?;

        let created_dt: DateTime<Utc> = created.into();
        let modified_dt: DateTime<Utc> = modified.into();

        Ok(LogFileInfo {
            path: log_path.to_string_lossy().to_string(),
            size: metadata.len(),
            created: created_dt.format("%Y-%m-%d %H:%M:%S").to_string(),
            modified: modified_dt.format("%Y-%m-%d %H:%M:%S").to_string(),
        })
    }

    /// 导出日志到指定路径
    pub async fn export_logs(&self, export_path: PathBuf) -> Result<(), String> {
        let logs = self.get_logs(None).await;

        let mut file = File::create(&export_path).map_err(|e| format!("创建导出文件失败: {}", e))?;

        for log in logs {
            let line = format!("[{}] [{}] [{}::{}] {}\n", log.timestamp, log.level.to_string(), log.module, log.function, log.message);
            file.write_all(line.as_bytes()).map_err(|e| format!("写入导出文件失败: {}", e))?;
        }

        self.log(LogLevel::Info, "LoggingService", "export_logs", &format!("日志已导出到: {}", export_path.display())).await?;

        Ok(())
    }

    /// 写入文件
    async fn write_to_file(&self, entry: &LogEntry) -> Result<(), String> {
        let mut file_writer = self.file_writer.lock().unwrap();

        if let Some(writer) = file_writer.as_mut() {
            let line = format!("[{}] [{}] [{}::{}] {}\n", entry.timestamp, entry.level.to_string(), entry.module, entry.function, entry.message);

            writer.write_all(line.as_bytes()).map_err(|e| format!("写入日志文件失败: {}", e))?;
            writer.flush().map_err(|e| format!("刷新日志文件失败: {}", e))?;
        }

        Ok(())
    }
}
