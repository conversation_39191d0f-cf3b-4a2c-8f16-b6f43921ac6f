use super::service::{LogEntry, LogFileInfo, LoggingService};
use std::path::PathBuf;
use tauri::State;

/// 获取日志列表
#[tauri::command]
pub async fn logs_get(
    logging_service: State<'_, LoggingService>,
    limit: Option<usize>,
) -> Result<Vec<LogEntry>, String> {
    Ok(logging_service.get_logs(limit).await)
}

/// 清空内存日志
#[tauri::command]
pub async fn logs_clear(logging_service: State<'_, LoggingService>) -> Result<(), String> {
    logging_service.clear_logs().await
}

/// 导出日志到文件
#[tauri::command]
pub async fn logs_export(
    logging_service: State<'_, LoggingService>,
    export_path: String,
) -> Result<(), String> {
    let path = PathBuf::from(export_path);
    logging_service.export_logs(path).await
}

/// 获取日志文件信息
#[tauri::command]
pub async fn logs_get_file_info(
    logging_service: State<'_, LoggingService>,
) -> Result<LogFileInfo, String> {
    logging_service.get_log_file_info().await
}

/// 兼容性命令 - 日志轮转（实际上是清空内存日志）
#[tauri::command]
pub async fn logs_rotate(logging_service: State<'_, LoggingService>) -> Result<(), String> {
    logging_service.clear_logs().await
}
