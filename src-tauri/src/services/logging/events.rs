use super::service::{LogEntry, LogLevel, LoggingService};
use tauri::{AppHandle, Emitter};

/// 日志事件处理器
pub struct LoggingEventHandler {
    logging_service: LoggingService,
}

impl LoggingEventHandler {
    pub fn new(logging_service: LoggingService) -> Self {
        Self { logging_service }
    }

    /// 发送日志条目到前端
    pub async fn emit_log_entry(&self, entry: &LogEntry) -> Result<(), String> {
        self.logging_service
            .app_handle()
            .emit("new_log_entry", entry)
            .map_err(|e| format!("发送日志事件失败: {}", e))
    }

    /// 发送日志状态更新
    pub async fn emit_log_status(&self, status: &str) -> Result<(), String> {
        self.logging_service
            .app_handle()
            .emit("log_status_changed", status)
            .map_err(|e| format!("发送日志状态事件失败: {}", e))
    }

    /// 批量发送日志条目
    pub async fn emit_log_batch(&self, entries: &[LogEntry]) -> Result<(), String> {
        self.logging_service
            .app_handle()
            .emit("log_batch", entries)
            .map_err(|e| format!("发送批量日志事件失败: {}", e))
    }
}

/// 日志事件监听器设置
pub async fn setup_logging_listeners(app_handle: AppHandle, logging_service: LoggingService) {
    let handler = LoggingEventHandler::new(logging_service.clone());

    // 监听来自其他服务的日志请求
    let app_handle_clone = app_handle.clone();
    let logging_service_clone = logging_service.clone();
    
    tokio::spawn(async move {
        // 这里可以设置其他服务的日志监听
        // 例如监听特定事件并记录日志
    });
}

/// 便捷的日志发送函数
pub async fn emit_log(
    app_handle: &AppHandle,
    level: LogLevel,
    module: &str,
    function: &str,
    message: &str,
) -> Result<(), String> {
    let entry = LogEntry::new(level, module, function, message);
    app_handle
        .emit("new_log_entry", &entry)
        .map_err(|e| format!("发送日志事件失败: {}", e))
}

/// 发送系统日志
pub async fn emit_system_log(app_handle: &AppHandle, message: &str) -> Result<(), String> {
    emit_log(app_handle, LogLevel::Info, "System", "emit_system_log", message).await
}

/// 发送错误日志
pub async fn emit_error_log(app_handle: &AppHandle, error: &str) -> Result<(), String> {
    emit_log(app_handle, LogLevel::Error, "System", "emit_error_log", error).await
}
