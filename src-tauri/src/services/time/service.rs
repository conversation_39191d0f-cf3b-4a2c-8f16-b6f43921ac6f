use chrono::{DateTime, Local, Utc};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tauri::{AppHandle, Emitter};
use tokio::sync::RwLock;

/// 时间同步信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeSync {
    /// 服务器时间（UTC）
    pub server_time: DateTime<Utc>,
    /// 本地时间（UTC）
    pub local_time: DateTime<Utc>,
    /// 时间差值（秒）- 正数表示服务器时间快于本地时间
    pub offset_seconds: i64,
    /// 同步时间戳
    pub synced_at: DateTime<Utc>,
}

impl TimeSync {
    /// 创建新的时间同步信息
    pub fn new(server_time: DateTime<Utc>, local_time: DateTime<Utc>) -> Self {
        let offset_seconds = server_time.timestamp() - local_time.timestamp();
        Self {
            server_time,
            local_time,
            offset_seconds,
            synced_at: Utc::now(),
        }
    }

    /// 获取当前的服务器时间（基于本地时间和偏移量计算）
    pub fn get_server_time(&self) -> DateTime<Utc> {
        let current_local = Utc::now();
        current_local + chrono::Duration::seconds(self.offset_seconds)
    }

    /// 获取当前的本地时间（转换为当地时区）
    pub fn get_local_time(&self) -> DateTime<Local> {
        Local::now()
    }

    /// 检查同步是否过期（超过1小时重新同步）
    pub fn is_expired(&self) -> bool {
        let now = Utc::now();
        (now - self.synced_at).num_hours() >= 1
    }
}

/// 时间服务
#[derive(Debug, Clone)]
pub struct TimeService {
    app_handle: AppHandle,
    time_sync: Arc<RwLock<Option<TimeSync>>>,
}

impl TimeService {
    /// 创建时间服务
    pub fn new(app_handle: AppHandle) -> Self {
        Self {
            app_handle,
            time_sync: Arc::new(RwLock::new(None)),
        }
    }

    /// 从HTTP响应头解析服务器时间
    pub fn parse_server_time_from_header(date_header: &str) -> Result<DateTime<Utc>, String> {
        println!("🕒 尝试解析时间字符串: '{}'", date_header);

        // 尝试多种常见的HTTP Date格式
        let formats = [
            "%a, %d %b %Y %H:%M:%S GMT", // Thu, 24 Jul 2025 06:59:47 GMT
            "%a, %d %b %Y %H:%M:%S %Z",  // Thu, 24 Jul 2025 06:59:47 GMT (通用时区)
            "%a, %d-%b-%Y %H:%M:%S GMT", // Thu, 24-Jul-2025 06:59:47 GMT
            "%d %b %Y %H:%M:%S GMT",     // 24 Jul 2025 06:59:47 GMT
        ];

        for format in &formats {
            println!("🕒 尝试格式: {}", format);
            if let Ok(parsed) = DateTime::parse_from_str(date_header, format) {
                println!("✅ 时间解析成功，使用格式: {}", format);
                return Ok(parsed.with_timezone(&Utc));
            }
        }

        // 如果所有格式都失败，尝试使用RFC2822格式（更宽松）
        if let Ok(parsed) = DateTime::parse_from_rfc2822(date_header) {
            println!("✅ 时间解析成功，使用RFC2822格式");
            return Ok(parsed.with_timezone(&Utc));
        }

        Err(format!(
            "无法解析时间格式: '{}', 尝试了所有常见格式",
            date_header
        ))
    }

    /// 更新服务器时间同步
    pub async fn update_server_time(&self, server_time: DateTime<Utc>) -> Result<(), String> {
        let local_time = Utc::now();
        let time_sync = TimeSync::new(server_time, local_time);

        println!(
            "🕒 更新服务器时间同步: 服务器时间={}, 本地时间={}, 偏移={}秒",
            server_time.format("%Y-%m-%d %H:%M:%S UTC"),
            local_time.format("%Y-%m-%d %H:%M:%S UTC"),
            time_sync.offset_seconds
        );

        {
            let mut sync = self.time_sync.write().await;
            *sync = Some(time_sync.clone());
        }

        // 发送时间同步事件到前端
        self.emit_time_sync_event(&time_sync).await?;

        Ok(())
    }

    /// 从HTTP响应头更新服务器时间
    pub async fn update_from_response_header(&self, date_header: &str) -> Result<(), String> {
        let server_time = Self::parse_server_time_from_header(date_header)?;
        self.update_server_time(server_time).await
    }

    /// 获取当前的服务器时间
    pub async fn get_server_time(&self) -> Option<DateTime<Utc>> {
        let sync = self.time_sync.read().await;
        sync.as_ref().map(|s| s.get_server_time())
    }

    /// 获取当前的本地时间（当地时区）
    pub async fn get_local_time(&self) -> DateTime<Local> {
        Local::now()
    }

    /// 获取时间偏移量（秒）
    pub async fn get_time_offset(&self) -> Option<i64> {
        let sync = self.time_sync.read().await;
        sync.as_ref().map(|s| s.offset_seconds)
    }

    /// 获取时间同步信息
    pub async fn get_time_sync(&self) -> Option<TimeSync> {
        let sync = self.time_sync.read().await;
        sync.clone()
    }

    /// 检查是否需要重新同步
    pub async fn needs_resync(&self) -> bool {
        let sync = self.time_sync.read().await;
        match sync.as_ref() {
            Some(s) => s.is_expired(),
            None => true, // 没有同步过，需要同步
        }
    }

    /// 验证时间（用于激活码等安全验证）
    pub async fn validate_time(&self, target_time: DateTime<Utc>) -> Result<bool, String> {
        let server_time = self
            .get_server_time()
            .await
            .ok_or("服务器时间未同步，无法验证时间")?;

        // 允许5分钟的误差
        let diff = (server_time.timestamp() - target_time.timestamp()).abs();
        Ok(diff <= 300) // 5分钟 = 300秒
    }

    /// 发送时间同步事件到前端
    async fn emit_time_sync_event(&self, time_sync: &TimeSync) -> Result<(), String> {
        #[derive(Serialize)]
        struct TimeSyncEvent {
            server_time: String,
            local_time: String,
            offset_seconds: i64,
            synced_at: String,
        }

        let event = TimeSyncEvent {
            server_time: time_sync.server_time.to_rfc3339(),
            local_time: time_sync.local_time.to_rfc3339(),
            offset_seconds: time_sync.offset_seconds,
            synced_at: time_sync.synced_at.to_rfc3339(),
        };

        self.app_handle
            .emit("time_sync_updated", &event)
            .map_err(|e| format!("发送时间同步事件失败: {}", e))
    }
}
