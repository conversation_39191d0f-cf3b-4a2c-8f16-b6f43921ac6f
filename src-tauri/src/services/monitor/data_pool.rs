use crate::models::config::MonitorItem;
use std::collections::{HashMap, HashSet};
use std::time::Instant;

/// 监控数据池，用于存储已知商品ID和管理内存使用
#[derive(Debug, Clone)]
pub struct MonitorDataPool {
    /// 关键词 -> 已知商品ID集合
    known_items: HashMap<String, HashSet<String>>,
    /// 数据池创建时间，用于清理策略
    created_at: Instant,
    /// 每个关键词最大保留商品数量
    max_items_per_keyword: usize,
}

impl MonitorDataPool {
    /// 创建新的数据池
    pub fn new() -> Self {
        Self {
            known_items: HashMap::new(),
            created_at: Instant::now(),
            max_items_per_keyword: 500, // 每个关键词最多保留500个商品ID
        }
    }

    /// 检查商品是否为新商品
    pub fn is_new_item(&self, keyword: &str, item_id: &str) -> bool {
        if let Some(known_set) = self.known_items.get(keyword) {
            !known_set.contains(item_id)
        } else {
            true // 如果关键词不存在，说明是新商品
        }
    }

    /// 添加新商品ID到数据池
    pub fn add_item(&mut self, keyword: &str, item_id: String) {
        let entry = self.known_items.entry(keyword.to_string()).or_insert_with(HashSet::new);

        // 如果达到容量上限，清理一些旧数据（简单策略：清理一半）
        if entry.len() >= self.max_items_per_keyword {
            let items_to_remove: Vec<String> = entry.iter().take(self.max_items_per_keyword / 2).cloned().collect();
            for item in items_to_remove {
                entry.remove(&item);
            }
        }

        entry.insert(item_id);
    }

    /// 批量检查新商品并添加到数据池
    pub fn filter_and_add_new_items(&mut self, keyword: &str, items: &[MonitorItem]) -> Vec<MonitorItem> {
        let mut new_items = Vec::new();

        for item in items {
            if self.is_new_item(keyword, &item.item_id) {
                new_items.push(item.clone());
                self.add_item(keyword, item.item_id.clone());
            }
        }

        new_items
    }

    /// 获取数据池统计信息
    pub fn get_stats(&self) -> (usize, usize) {
        let keyword_count = self.known_items.len();
        let total_items: usize = self.known_items.values().map(|set| set.len()).sum();
        (keyword_count, total_items)
    }

    /// 清理数据池（如果运行时间过长）
    pub fn cleanup_if_needed(&mut self) -> bool {
        // 如果数据池存活超过1小时，清理一半数据
        if self.created_at.elapsed().as_secs() > 3600 {
            for (_, item_set) in self.known_items.iter_mut() {
                let items_to_remove: Vec<String> = item_set.iter().take(item_set.len() / 2).cloned().collect();
                for item in items_to_remove {
                    item_set.remove(&item);
                }
            }
            self.created_at = Instant::now(); // 重置创建时间
            true
        } else {
            false
        }
    }

    /// 清空指定关键词的数据
    pub fn clear_keyword_data(&mut self, keyword: &str) {
        self.known_items.remove(keyword);
    }

    /// 清空所有数据
    pub fn clear_all(&mut self) {
        self.known_items.clear();
        self.created_at = Instant::now();
    }

    /// 获取关键词的商品数量
    pub fn get_keyword_item_count(&self, keyword: &str) -> usize {
        self.known_items.get(keyword).map(|set| set.len()).unwrap_or(0)
    }

    /// 获取所有关键词列表
    pub fn get_keywords(&self) -> Vec<String> {
        self.known_items.keys().cloned().collect()
    }

    /// 设置每个关键词的最大商品数量
    pub fn set_max_items_per_keyword(&mut self, max_items: usize) {
        self.max_items_per_keyword = max_items;
    }

    /// 获取每个关键词的最大商品数量
    pub fn get_max_items_per_keyword(&self) -> usize {
        self.max_items_per_keyword
    }
}

impl Default for MonitorDataPool {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_data_pool_basic_operations() {
        let mut pool = MonitorDataPool::new();

        // 测试新商品检查
        assert!(pool.is_new_item("相机", "item1"));

        // 添加商品
        pool.add_item("相机", "item1".to_string());
        assert!(!pool.is_new_item("相机", "item1"));
        assert!(pool.is_new_item("相机", "item2"));
    }

    #[test]
    fn test_data_pool_stats() {
        let mut pool = MonitorDataPool::new();

        pool.add_item("相机", "item1".to_string());
        pool.add_item("相机", "item2".to_string());
        pool.add_item("手机", "item3".to_string());

        let (keyword_count, total_items) = pool.get_stats();
        assert_eq!(keyword_count, 2);
        assert_eq!(total_items, 3);
    }

    #[test]
    fn test_data_pool_cleanup() {
        let mut pool = MonitorDataPool::new();
        pool.set_max_items_per_keyword(3);

        // 添加超过限制的商品
        for i in 0..5 {
            pool.add_item("相机", format!("item{}", i));
        }

        // 检查是否进行了清理
        let count = pool.get_keyword_item_count("相机");
        assert!(count <= 3);
    }
}
