use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tauri::{App<PERSON>andle, Emitter, Manager, WebviewUrl, WebviewWindow, WebviewWindowBuilder};
use tokio::sync::Mutex;

/// 浏览器窗口实例 - 简化版本，只提供基本的窗口访问
#[derive(Clone)]
pub struct BrowserWindow {
    label: String,
    app_handle: AppHandle,
}

impl BrowserWindow {
    /// 创建新的浏览器窗口实例
    pub fn new(label: String, app_handle: AppHandle) -> Self {
        Self { label, app_handle }
    }

    /// 获取窗口标签
    pub fn label(&self) -> &str {
        &self.label
    }

    /// 获取窗口实例
    pub fn window(&self) -> Option<WebviewWindow> {
        self.app_handle.get_webview_window(&self.label)
    }
}

/// 浏览器窗口配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserWindowConfig {
    pub width: f64,
    pub height: f64,
    pub x: Option<f64>,
    pub y: Option<f64>,
    pub title: String,
    pub resizable: bool,
    pub closable: bool,
    pub minimizable: bool,
    pub maximizable: bool,
    pub decorations: bool,
    pub always_on_top: bool,
    pub skip_taskbar: bool,
    pub fullscreen: bool,
    pub center: bool,
    pub user_data_path: Option<String>, // 用户数据目录路径
}

impl Default for BrowserWindowConfig {
    fn default() -> Self {
        Self {
            width: 1200.0,
            height: 800.0,
            x: None,
            y: None,
            title: "浏览器窗口".to_string(),
            resizable: true,
            closable: true,
            minimizable: true,
            maximizable: true,
            decorations: true,
            always_on_top: false,
            skip_taskbar: false,
            fullscreen: false,
            center: true,
            user_data_path: None,
        }
    }
}

impl BrowserWindowConfig {
    /// 创建小窗口配置
    pub fn small() -> Self {
        Self {
            width: 800.0,
            height: 600.0,
            ..Default::default()
        }
    }

    /// 创建大窗口配置
    pub fn large() -> Self {
        Self {
            width: 1600.0,
            height: 1200.0,
            ..Default::default()
        }
    }

    /// 创建全屏配置
    pub fn fullscreen() -> Self {
        Self {
            fullscreen: true,
            ..Default::default()
        }
    }

    /// 创建最小化配置
    pub fn minimized() -> Self {
        Self {
            width: 400.0,
            height: 300.0,
            ..Default::default()
        }
    }

    /// 设置窗口大小
    pub fn with_size(mut self, width: f64, height: f64) -> Self {
        self.width = width;
        self.height = height;
        self
    }

    /// 设置窗口标题
    pub fn with_title(mut self, title: &str) -> Self {
        self.title = title.to_string();
        self
    }

    /// 设置窗口位置
    pub fn with_position(mut self, x: f64, y: f64) -> Self {
        self.x = Some(x);
        self.y = Some(y);
        self.center = false; // 设置位置后不再居中
        self
    }

    /// 设置置顶
    pub fn with_always_on_top(mut self, always_on_top: bool) -> Self {
        self.always_on_top = always_on_top;
        self
    }

    /// 设置可调整大小
    pub fn with_resizable(mut self, resizable: bool) -> Self {
        self.resizable = resizable;
        self
    }

    /// 设置用户数据目录
    pub fn with_user_data_path(mut self, path: String) -> Self {
        self.user_data_path = Some(path);
        self
    }
}

/// 浏览器窗口信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserWindowInfo {
    pub label: String,
    pub url: String,
    pub title: String,
    pub config: BrowserWindowConfig,
    pub created_at: String,
}

/// 纯粹的浏览器服务 - 完全解耦，只处理浏览器窗口管理
#[derive(Clone)]
pub struct BrowserService {
    app_handle: AppHandle,
    active_browsers: Arc<Mutex<HashMap<String, BrowserWindowInfo>>>,
}

impl BrowserService {
    /// 创建新的浏览器服务实例
    pub fn new(app_handle: AppHandle) -> Self {
        Self {
            app_handle,
            active_browsers: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// 获取app_handle的引用
    pub fn app_handle(&self) -> &AppHandle {
        &self.app_handle
    }

    /// 打开浏览器窗口，返回 BrowserWindow 实例和可能的数据存储标识符
    pub async fn open_browser(
        &self,
        url: &str,
        label: Option<String>,
        config: Option<BrowserWindowConfig>,
    ) -> Result<(BrowserWindow, Option<[u8; 16]>), String> {
        // 直接在这里处理identifier逻辑
        let window_config = config.unwrap_or_default();
        let window_label =
            label.unwrap_or_else(|| format!("browser_{}", chrono::Utc::now().timestamp()));

        // 设置用户数据目录 - 统一使用data_store_identifier
        let mut data_store_identifier: Option<[u8; 16]> = None;

        if let Some(user_data_path) = &window_config.user_data_path {
            // 统一使用data_store_identifier（所有平台）
            let session_id = std::path::Path::new(user_data_path)
                .file_name()
                .and_then(|name| name.to_str())
                .unwrap_or("default");

            // 使用 SHA256 生成稳定的哈希（与AccountService保持一致）
            use sha2::{Digest, Sha256};

            let mut hasher = Sha256::new();
            hasher.update(session_id.as_bytes());
            let hash = hasher.finalize();

            // 取前16字节作为标识符
            let mut identifier = [0u8; 16];
            identifier.copy_from_slice(&hash[0..16]);

            data_store_identifier = Some(identifier);
        }

        // 调用原有的方法创建窗口
        let browser_window = self
            .open_browser_with_domains(url, Some(window_label), Some(window_config), None)
            .await?;

        Ok((browser_window, data_store_identifier))
    }

    /// 打开浏览器窗口（带域名限制），返回 BrowserWindow 实例
    pub async fn open_browser_with_domains(
        &self,
        url: &str,
        label: Option<String>,
        config: Option<BrowserWindowConfig>,
        allowed_domains: Option<Vec<String>>,
    ) -> Result<BrowserWindow, String> {
        let window_label = label.unwrap_or_else(|| format!("browser_{}", uuid::Uuid::new_v4()));
        let window_config = config.unwrap_or_default();

        // 检查窗口是否已存在
        if self.app_handle.get_webview_window(&window_label).is_some() {
            return Err(format!("窗口 {} 已存在", window_label));
        }

        // 创建窗口构建器
        let mut builder = WebviewWindowBuilder::new(
            &self.app_handle,
            &window_label,
            WebviewUrl::External(url.parse().map_err(|e| format!("无效的URL: {}", e))?),
        )
        .title(&window_config.title)
        .inner_size(window_config.width, window_config.height)
        .resizable(window_config.resizable)
        .closable(window_config.closable)
        .minimizable(window_config.minimizable)
        .maximizable(window_config.maximizable)
        .decorations(window_config.decorations)
        .always_on_top(window_config.always_on_top)
        .skip_taskbar(window_config.skip_taskbar)
        .fullscreen(window_config.fullscreen);

        // 如果设置了自定义数据目录，添加唯一的用户代理
        if let Some(user_data_path) = &window_config.user_data_path {
            let session_id = std::path::Path::new(user_data_path)
                .file_name()
                .and_then(|name| name.to_str())
                .unwrap_or("unknown");

            let custom_user_agent = format!(
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 GoldfishSession/{}",
                session_id
            );

            builder = builder.user_agent(&custom_user_agent);
            println!("🔧 设置自定义用户代理: {}", custom_user_agent);
        }

        // 如果提供了允许的域名列表，添加导航拦截器
        if let Some(domains) = allowed_domains {
            builder = builder.on_navigation(move |url| {
                let url_str = url.as_str();

                // 检查是否为允许的域名或路径
                let is_allowed = domains
                    .iter()
                    .any(|allowed| Self::match_url_pattern(url_str, allowed));

                if is_allowed {
                    true
                } else {
                    println!("❌ 阻止导航到: {}", url_str);
                    false
                }
            });
        }

        // 设置位置
        if let (Some(x), Some(y)) = (window_config.x, window_config.y) {
            builder = builder.position(x, y);
        } else if window_config.center {
            builder = builder.center();
        }

        // 设置用户数据目录 - 平台兼容性处理
        let mut data_store_identifier: Option<[u8; 16]> = None;

        if let Some(user_data_path) = &window_config.user_data_path {
            println!("🗂️ 设置浏览器用户数据目录: {}", user_data_path);

            // 检查目录是否存在
            let path = std::path::PathBuf::from(user_data_path);
            if path.exists() {
                println!("✅ 会话目录已存在: {}", user_data_path);
            } else {
                println!("❌ 会话目录不存在: {}", user_data_path);
            }

            // 统一使用data_store_identifier（所有平台）
            let session_id = std::path::Path::new(user_data_path)
                .file_name()
                .and_then(|name| name.to_str())
                .unwrap_or("default");

            // 使用 SHA256 生成稳定的哈希（与AccountService保持一致）
            use sha2::{Digest, Sha256};

            let mut hasher = Sha256::new();
            hasher.update(session_id.as_bytes());
            let hash = hasher.finalize();

            // 取前16字节作为标识符
            let mut identifier = [0u8; 16];
            identifier.copy_from_slice(&hash[0..16]);

            println!(
                "🔑 统一使用数据存储标识符: {:?} (来源: {})",
                identifier, session_id
            );
            builder = builder.data_store_identifier(identifier);
            data_store_identifier = Some(identifier);
        } else {
            println!("⚠️ 未设置用户数据目录，将使用默认目录");
        }

        // 创建窗口
        let window = builder
            .build()
            .map_err(|e| format!("创建浏览器窗口失败: {}", e))?;

        // 记录窗口信息
        let window_info = BrowserWindowInfo {
            label: window_label.clone(),
            url: url.to_string(),
            title: window_config.title.clone(),
            config: window_config,
            created_at: chrono::Utc::now().to_rfc3339(),
        };

        {
            let mut browsers = self.active_browsers.lock().await;
            browsers.insert(window_label.clone(), window_info);
        }

        // 发送窗口创建事件
        self.emit_browser_opened(&window_label, url).await?;

        // 创建并返回 BrowserWindow 实例
        let browser_window = BrowserWindow::new(window_label, self.app_handle.clone());
        Ok(browser_window)
    }

    /// 关闭浏览器窗口
    pub async fn close_browser(&self, label: &str) -> Result<(), String> {
        if let Some(window) = self.app_handle.get_webview_window(label) {
            window.close().map_err(|e| format!("关闭窗口失败: {}", e))?;

            // 从记录中移除
            {
                let mut browsers = self.active_browsers.lock().await;
                browsers.remove(label);
            }

            // 发送窗口关闭事件
            self.emit_browser_closed(label).await?;

            Ok(())
        } else {
            Err(format!("窗口 {} 不存在", label))
        }
    }

    /// 关闭所有浏览器窗口
    pub async fn close_all_browsers(&self) -> Result<(), String> {
        let labels: Vec<String> = {
            let browsers = self.active_browsers.lock().await;
            browsers.keys().cloned().collect()
        };

        for label in labels {
            if let Err(e) = self.close_browser(&label).await {
                eprintln!("关闭窗口 {} 失败: {}", label, e);
            }
        }

        Ok(())
    }

    /// 获取活动的浏览器窗口列表
    pub async fn get_active_browsers(&self) -> HashMap<String, BrowserWindowInfo> {
        let browsers = self.active_browsers.lock().await;
        browsers.clone()
    }

    /// 检查窗口是否存在
    pub fn is_window_exists(&self, label: &str) -> bool {
        self.app_handle.get_webview_window(label).is_some()
    }

    /// 聚焦窗口
    pub async fn focus_window(&self, label: &str) -> Result<(), String> {
        if let Some(window) = self.app_handle.get_webview_window(label) {
            window
                .set_focus()
                .map_err(|e| format!("聚焦窗口失败: {}", e))?;
            Ok(())
        } else {
            Err(format!("窗口 {} 不存在", label))
        }
    }

    /// 最小化窗口
    pub async fn minimize_window(&self, label: &str) -> Result<(), String> {
        if let Some(window) = self.app_handle.get_webview_window(label) {
            window
                .minimize()
                .map_err(|e| format!("最小化窗口失败: {}", e))?;
            Ok(())
        } else {
            Err(format!("窗口 {} 不存在", label))
        }
    }

    /// 最大化窗口
    pub async fn maximize_window(&self, label: &str) -> Result<(), String> {
        if let Some(window) = self.app_handle.get_webview_window(label) {
            window
                .maximize()
                .map_err(|e| format!("最大化窗口失败: {}", e))?;
            Ok(())
        } else {
            Err(format!("窗口 {} 不存在", label))
        }
    }

    /// 设置窗口大小
    pub async fn resize_window(&self, label: &str, width: f64, height: f64) -> Result<(), String> {
        if let Some(window) = self.app_handle.get_webview_window(label) {
            window
                .set_size(tauri::Size::Physical(tauri::PhysicalSize {
                    width: width as u32,
                    height: height as u32,
                }))
                .map_err(|e| format!("调整窗口大小失败: {}", e))?;
            Ok(())
        } else {
            Err(format!("窗口 {} 不存在", label))
        }
    }

    /// 发送浏览器打开事件
    async fn emit_browser_opened(&self, label: &str, url: &str) -> Result<(), String> {
        let payload = serde_json::json!({
            "label": label,
            "url": url,
            "action": "opened",
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        self.app_handle
            .emit("browser_window_event", &payload)
            .map_err(|e| format!("发送浏览器打开事件失败: {}", e))
    }

    /// 发送浏览器关闭事件
    async fn emit_browser_closed(&self, label: &str) -> Result<(), String> {
        let payload = serde_json::json!({
            "label": label,
            "action": "closed",
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        self.app_handle
            .emit("browser_window_event", &payload)
            .map_err(|e| format!("发送浏览器关闭事件失败: {}", e))
    }

    /// 通用通配符匹配方法
    /// 支持多种通配符模式：
    /// - *.example.com (子域名通配符)
    /// - www.*.example.com (中间通配符)
    /// - example.com/*/path (路径通配符)
    /// - example.com/api/*/data (多段路径通配符)
    fn match_url_pattern(url: &str, pattern: &str) -> bool {
        // 如果没有通配符，直接进行精确匹配或包含匹配
        if !pattern.contains('*') {
            let result = url == pattern || url.contains(pattern);
            return result;
        }

        // 特殊处理域名通配符
        if pattern.starts_with("*.") {
            let domain_pattern = &pattern[2..]; // 移除 "*."

            // 从URL中提取域名
            if let Ok(parsed_url) = url::Url::parse(url) {
                if let Some(domain) = parsed_url.domain() {
                    let result = domain == domain_pattern
                        || domain.ends_with(&format!(".{}", domain_pattern));
                    return result;
                }
            }
        }

        // 将模式转换为正则表达式（用于路径通配符等）
        let regex_pattern = regex::escape(pattern).replace(r"\*", ".*");

        // 编译正则表达式并进行匹配
        if let Ok(regex) = regex::Regex::new(&format!("^{}$", regex_pattern)) {
            let result = regex.is_match(url);
            result
        } else {
            // 如果正则表达式编译失败，回退到简单的包含匹配
            println!("⚠️ 正则表达式编译失败，回退到包含匹配: {}", pattern);
            let result = url.contains(&pattern.replace("*", ""));
            println!("📝 回退匹配结果: {}", result);
            result
        }
    }
}
