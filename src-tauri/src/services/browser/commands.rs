use super::service::{BrowserService, BrowserWindowConfig, BrowserWindowInfo};
use std::collections::HashMap;
use tauri::State;

/// 打开浏览器窗口
#[tauri::command]
pub async fn browser_open(
    browser_service: State<'_, BrowserService>,
    url: String,
    label: Option<String>,
) -> Result<String, String> {
    let (browser_window, _data_store_identifier) =
        browser_service.open_browser(&url, label, None).await?;
    Ok(browser_window.label().to_string())
}

/// 使用配置打开浏览器窗口
#[tauri::command]
pub async fn browser_open_with_config(
    browser_service: State<'_, BrowserService>,
    url: String,
    label: Option<String>,
    config: BrowserWindowConfig,
) -> Result<String, String> {
    let (browser_window, _data_store_identifier) = browser_service
        .open_browser(&url, label, Some(config))
        .await?;
    Ok(browser_window.label().to_string())
}

/// 关闭浏览器窗口
#[tauri::command]
pub async fn browser_close(
    browser_service: State<'_, BrowserService>,
    label: String,
) -> Result<(), String> {
    browser_service.close_browser(&label).await
}

/// 关闭所有浏览器窗口
#[tauri::command]
pub async fn browser_close_all(browser_service: State<'_, BrowserService>) -> Result<(), String> {
    browser_service.close_all_browsers().await
}

/// 获取活动的浏览器窗口列表
#[tauri::command]
pub async fn browser_get_active(
    browser_service: State<'_, BrowserService>,
) -> Result<HashMap<String, BrowserWindowInfo>, String> {
    Ok(browser_service.get_active_browsers().await)
}

/// 检查窗口是否存在
#[tauri::command]
pub async fn browser_window_exists(
    browser_service: State<'_, BrowserService>,
    label: String,
) -> Result<bool, String> {
    Ok(browser_service.is_window_exists(&label))
}

/// 聚焦窗口
#[tauri::command]
pub async fn browser_focus_window(
    browser_service: State<'_, BrowserService>,
    label: String,
) -> Result<(), String> {
    browser_service.focus_window(&label).await
}

/// 最小化窗口
#[tauri::command]
pub async fn browser_minimize_window(
    browser_service: State<'_, BrowserService>,
    label: String,
) -> Result<(), String> {
    browser_service.minimize_window(&label).await
}

/// 最大化窗口
#[tauri::command]
pub async fn browser_maximize_window(
    browser_service: State<'_, BrowserService>,
    label: String,
) -> Result<(), String> {
    browser_service.maximize_window(&label).await
}

/// 调整窗口大小
#[tauri::command]
pub async fn browser_resize_window(
    browser_service: State<'_, BrowserService>,
    label: String,
    width: f64,
    height: f64,
) -> Result<(), String> {
    browser_service.resize_window(&label, width, height).await
}
