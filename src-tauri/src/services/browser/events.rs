use super::service::<PERSON>rowserService;
use tauri::{AppHand<PERSON>, Emitter};

/// 浏览器事件处理器
pub struct BrowserEventHandler {
    browser_service: BrowserService,
}

impl BrowserEventHandler {
    pub fn new(browser_service: BrowserService) -> Self {
        Self { browser_service }
    }

    /// 发送浏览器窗口打开事件
    pub async fn emit_window_opened(&self, label: &str, url: &str) -> Result<(), String> {
        let payload = serde_json::json!({
            "label": label,
            "url": url,
            "action": "opened",
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        self.browser_service
            .app_handle()
            .emit("browser_window_opened", &payload)
            .map_err(|e| format!("发送浏览器窗口打开事件失败: {}", e))
    }

    /// 发送浏览器窗口关闭事件
    pub async fn emit_window_closed(&self, label: &str) -> Result<(), String> {
        let payload = serde_json::json!({
            "label": label,
            "action": "closed",
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        self.browser_service
            .app_handle()
            .emit("browser_window_closed", &payload)
            .map_err(|e| format!("发送浏览器窗口关闭事件失败: {}", e))
    }

    /// 发送浏览器窗口聚焦事件
    pub async fn emit_window_focused(&self, label: &str) -> Result<(), String> {
        let payload = serde_json::json!({
            "label": label,
            "action": "focused",
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        self.browser_service
            .app_handle()
            .emit("browser_window_focused", &payload)
            .map_err(|e| format!("发送浏览器窗口聚焦事件失败: {}", e))
    }

    /// 发送浏览器窗口调整大小事件
    pub async fn emit_window_resized(&self, label: &str, width: f64, height: f64) -> Result<(), String> {
        let payload = serde_json::json!({
            "label": label,
            "action": "resized",
            "width": width,
            "height": height,
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        self.browser_service
            .app_handle()
            .emit("browser_window_resized", &payload)
            .map_err(|e| format!("发送浏览器窗口调整大小事件失败: {}", e))
    }

    /// 发送浏览器状态更新事件
    pub async fn emit_browser_status(&self, status: &str, message: &str) -> Result<(), String> {
        let payload = serde_json::json!({
            "status": status,
            "message": message,
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        self.browser_service
            .app_handle()
            .emit("browser_status", &payload)
            .map_err(|e| format!("发送浏览器状态事件失败: {}", e))
    }
}

/// 浏览器事件监听器设置
pub async fn setup_browser_listeners(app_handle: AppHandle, browser_service: BrowserService) {
    let handler = BrowserEventHandler::new(browser_service.clone());

    // 监听窗口事件
    let app_handle_clone = app_handle.clone();
    let browser_service_clone = browser_service.clone();
    
    tokio::spawn(async move {
        // 这里可以设置窗口事件监听器
        // 例如监听窗口关闭、最小化等系统事件
    });
}

/// 便捷的浏览器事件发送函数
pub async fn emit_browser_event(
    app_handle: &AppHandle,
    event_name: &str,
    label: &str,
    action: &str,
) -> Result<(), String> {
    let payload = serde_json::json!({
        "label": label,
        "action": action,
        "timestamp": chrono::Utc::now().to_rfc3339()
    });

    app_handle
        .emit(event_name, &payload)
        .map_err(|e| format!("发送浏览器事件失败: {}", e))
}

/// 发送浏览器错误事件
pub async fn emit_browser_error(
    app_handle: &AppHandle,
    error: &str,
    label: Option<&str>,
) -> Result<(), String> {
    let payload = serde_json::json!({
        "error": error,
        "label": label,
        "timestamp": chrono::Utc::now().to_rfc3339()
    });

    app_handle
        .emit("browser_error", &payload)
        .map_err(|e| format!("发送浏览器错误事件失败: {}", e))
}
